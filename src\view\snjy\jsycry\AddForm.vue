<template>
  <div class="psychiatric-management-registration">
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :responsive="true"
      @card-action="handleCardAction"
      @bottom-action="handleBottomAction"
      @collapse-change="handleCollapseChange"
    >
      <!-- 左侧人员信息面板 -->
      <template #left>
        <personnel-selector
          v-model="personInfo.jgrybm"
          title="选择在押人员"
          :enableScan=false
          :personnelType=ryzt
          :show-case-info="false"
          placeholder="请选择需要操作的在押人员"
          @change="handleSelectPersonnel"
        />
      </template>

      <!-- 业务登记表单 -->
      <template #business-registration>
        <DynamicForm
          ref="businessForm"
          v-model="businessFormData"
          :config="businessFormConfig"
          :mode="formMode"
          :loading="loading"
          @field-change="handleBusinessFieldChange"
          @validate="handleBusinessValidate"
        />
      </template>
    </DetailCardLayout>
  </div>
</template>

<script>
import {FIELD_TYPES, FORM_MODES} from '@/components/dynamic-form/types'
import {mapActions} from 'vuex'

export default {
  name: 'PsychiatricManagementRegistration',
  props: {
    pk: String,
    mode: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      ryzt: 'ZS',
      loading: false,
      formMode: FORM_MODES.CREATE,

      // 人员信息
      personInfo: {
        jgrybm: ''
      },

      // 左侧面板配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true,
        data: {}
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'business-registration',
          title: '重点病人',
          icon: 'ios-medical',
          iconColor: '#5b8ff9',
          slot: 'business-registration',
          showHeader: false
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ],

      // 业务登记表单数据
      businessFormData: {
        // 基本信息
        jgrybm: '', // 被监管人员编码
        jgryxm: '', // 监管人员姓名

        // 精神病相关信息
        cnsltPreDiag: '', // 会诊初步诊断 字典ZD_JSB_HZCBZD
        controlReason: '', // 列控原因
        controlTime: '', // 列控时间
        managementLevel: '', // 列管等级 字典：ZD_JSB_LGDJ
        treatmentOutcome: '', // 治疗后是否好转 1是0否

        // 经办信息（系统自动填充）
        operatePolice: '', // 登记民警
        operatePoliceSfzh: '', // 登记民警身份证号
        operateTime: '', // 登记时间
        status: '1' // 状态：1-正常管理，2-已解除
      }
    }
  },

  computed: {
    // 业务登记表单配置
    businessFormConfig() {
      return [
        {
          title: '重点病人',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showTitle: true,
          fields: [
            {
              key: 'cnsltPreDiag',
              label: '会诊初步诊断',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_JSB_HZCBZD', // 字典：会诊初步诊断
              props: {
                placeholder: '请选择会诊初步诊断',
                multiple: false
              }
            },
            {
              key: 'controlTime',
              label: '列控时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择列控时间',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'controlReason',
              label: '列控原因',
              type: FIELD_TYPES.TEXTAREA,
              required: false,
              span: 24,
              props: {
                placeholder: '请输入列控原因',
                rows: 3
              }
            },
            {
              key: 'managementLevel',
              label: '列管等级',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_JSB_LGDJ', // 字典：列管等级
              props: {
                placeholder: '请选择列管等级',
                multiple: false
              }
            },
            {
              key: 'treatmentOutcome',
              label: '治疗后是否好转',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_TYSFDM', // 字典：是否
              props: {
                placeholder: '请选择',
                multiple: false
              }
            }
          ]
        }
      ]
    }
  },

  mounted() {
    this.initializeData()
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),

    // 初始化数据
    initializeData() {
      // 处理路由参数
      const {mode, ryId, recordId} = this.$route.query

      if (mode) {
        this.formMode = mode === 'view' ? FORM_MODES.VIEW :
          mode === 'edit' ? FORM_MODES.EDIT :
            FORM_MODES.CREATE
      }

      if (ryId) {
        this.personInfo.jgrybm = ryId
        this.businessFormData.jgrybm = ryId
      }

      if (recordId && mode !== 'add') {
        this.loadRecordData(recordId)
      }
    },

    // 获取当前日期时间
    getCurrentDateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // 加载记录数据
    loadRecordData(recordId) {
      this.loading = true
      this.authGetRequest({
        url: this.$path.psychiatric_get,
        params: {id: recordId}
      }).then(res => {
        this.loading = false
        if (res.success && res.data) {
          // 合并数据，保留现有字段
          this.businessFormData = {...this.businessFormData, ...res.data}
          this.personInfo.jgrybm = res.data.jgrybm || ''

          // 如果有人员姓名，也更新到人员信息中
          if (res.data.jgryxm) {
            this.businessFormData.jgryxm = res.data.jgryxm
          }
        } else {
          this.$Message.error(res.msg || '加载数据失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('加载数据失败：', error)
      })
    },

    // 处理人员选择
    handleSelectPersonnel(personnelData, jgrybm) {
      console.log('选择人员:', personnelData, jgrybm)
      this.personInfo.jgrybm = jgrybm

      // 同时更新表单数据中的人员信息
      this.businessFormData.jgrybm = jgrybm
      if (personnelData && personnelData.xm) {
        this.businessFormData.jgryxm = personnelData.xm
      }
    },

    // 处理收缩变化
    handleCollapseChange(event) {
      console.log('面板收缩状态变化:', event)
    },

    // 处理卡片操作
    handleCardAction(event) {
      console.log('卡片操作:', event)
    },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleBack()
          break
        case 'submit':
          this.handleSubmit()
          break
      }
    },

    // 返回
    handleBack() {
      this.$emit('toback')
    },

    // 提交
    handleSubmit() {
      this.validateAndSave()
    },

    // 验证并保存
    validateAndSave() {
      console.log('开始表单验证...')

      // 验证必填字段
      if (!this.businessFormData.jgrybm) {
        this.$Message.error('请先选择监管人员')
        return
      }

      if (!this.businessFormData.jgryxm) {
        this.$Message.error('请先选择监管人员')
        return
      }

      // 验证表单
      this.$refs.businessForm?.validate().then(valid => {
        console.log('表单验证结果:', valid)

        if (valid) {
          console.log('验证通过，开始保存数据')
          this.saveData()
        } else {
          console.log('验证失败，阻止提交')
          this.$Message.error('请检查表单填写是否完整')
        }
      }).catch(error => {
        console.error('表单验证异常：', error)
        this.$Message.error('表单验证失败')
      })
    },

    // 保存数据
    saveData() {
      this.loading = true

      // 准备提交数据，过滤掉空值
      const submitData = this.prepareSubmitData()

      // 调用精神病管理登记创建接口
      this.authPostRequest({
        url: this.$path.psychiatric_create,
        params: submitData
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          this.$Message.success('重点病人成功')
          this.handleBack()
        } else {
          this.$Message.error(res.msg || res.message || '重点病人失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('提交失败：', error)
      })
    },

    // 准备提交数据
    prepareSubmitData() {
      const data = {...this.businessFormData}
      // 过滤掉空值和undefined
      Object.keys(data).forEach(key => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
          delete data[key]
        }
      })

      // 确保必填字段存在
      if (!data.jgrybm) {
        data.jgrybm = this.personInfo.jgrybm
      }

      // 设置操作时间为当前时间
      data.operateTime = this.getCurrentDateTime()

      return data
    },

    // 业务表单字段变化
    handleBusinessFieldChange(key, value, formData) {
      console.log('业务表单字段变化:', key, value, typeof value)

      // 特别关注日期时间字段的变化
      if (key.includes('Time') || key.includes('Date')) {
        console.log('日期时间字段变化详情:', {
          key,
          value,
          valueType: typeof value,
          isDate: value instanceof Date,
          formData: formData[key]
        })
      }

      // 确保数据同步到父组件的 businessFormData
      this.businessFormData = {...this.businessFormData, ...formData}
    },

    // 业务表单验证
    handleBusinessValidate(prop, valid, message) {
      console.log('业务表单验证:', prop, valid, message)
    },

    // 所有文件上传完成回调
    fileCompleteFileCertUrl(files) {
      console.log('所有文件上传完成:', files)
      this.$Message.success('所有文件上传完成')

      // 更新表单数据
      this.businessFormData.attUrl = JSON.stringify(files);
    }
  }
}
</script>

<style lang="less" scoped>
.psychiatric-management-registration {
  height: 100%;

  // 修复文件上传组件样式
  /deep/ .upload-box {
    width: 100%;

    .file-item {
      width: 100%;
      max-width: 500px;
      height: auto;
      min-height: 55px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(206, 224, 240, 1);
      border-radius: 4px;
      display: flex;
      padding: 10px;
      margin-bottom: 10px;
      box-sizing: border-box;
    }

    .file-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
      min-width: 0; // 防止文本溢出
    }

    .file-text {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;

      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
      }

      .file-size {
        flex-shrink: 0;
        color: #999;
        font-size: 12px;
      }
    }

    .file-img {
      width: 32px;
      height: 32px;
      margin-right: 10px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .space-btw {
      display: flex;
      align-items: center;
      line-height: normal;

      span {
        margin: 0 3px;
      }
    }

    // 上传按钮样式
    .ivu-btn {
      margin-bottom: 15px;

      .ivu-icon {
        margin-right: 5px;
        position: relative;
        top: 2px;
      }
    }

    // 进度条样式
    .ivu-progress {
      margin-top: 5px;
    }
  }

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }

  // 文件上传字段特殊处理
  /deep/ .ivu-form-item {
    &:has(.upload-box) {
      .ivu-form-item-content {
        min-height: auto;
      }
    }
  }
}
</style>
