import {acpCom} from './base'

export default{
  // 看守所收押登记
  app_inRecordJlsCreate: acpCom + '/acp/db/inRecordJls/create',
  // 看守所收押登记(更新)
  app_inRecordJlsUpdate: acpCom + '/acp/db/inRecordJls/update',
  // 获取看守所收押登记
  app_inRecordJlsGet: acpCom + '/acp/db/inRecordJls/get',
  // 获取看守所收押登记列表
  app_inRecordJlsGetList: acpCom +  '/acp/db/inRecordJls/list',
  // 获取社会关系列表
  app_getSocialRelationsList: acpCom + '/acp/db/dbSocialRelations/list',
  // 根据身份证号和监所编号查询待入所信息
  app_getRecordByzjhm: acpCom + '/acp/pm/prisonerPreIn/getRecordByzjhm',
  // 根据身份证号和监所编号查询待入所信息
  app_getRecordByzjhmJls: acpCom +'/acp/pm/prisonerPreIn/getRecordByzjhmJls',
  // 入所健康检查登记
  app_healthCheckCreate: acpCom + '/acp/db/healthCheck/create',
  // 入所健康检查登记(更新)
  app_healthCheckUpdate: acpCom + '/acp/db/healthCheck/update',
  // 获取入所健康检查登记
  app_healthCheckGet: acpCom + '/acp/db/healthCheck/get',
  // 通过人员编号获取入所健康检查登记信息
  app_healthCheckGetByRybh: acpCom + '/acp/db/healthCheck/getByRybh',
  // 获得基本信息以及医生意见信息
  app_getInRecordJlsCombineInfo: acpCom + '/acp/db/inRecordJls/getCombineInfo',
  // 随身物品登记
  app_personalEffectsSubCreateBatch: acpCom + '/acp/db/personalEffectsSub/create/batch',
  // 随身物品获取
  app_personalEffectsSubGetList: acpCom + '/acp/db/personalEffectsSub/list',
  // 生物信息采集-提交-暂存
  app_biometricInfoAddInformation: acpCom +'/acp/db/biometricInfo/addInformation',
  // 根据人员编号获取人员信息
  app_getInRecordJlsPrisonerInfo: acpCom + '/acp/db/inRecordJls/getPrisonerInfo',
  // 创建实战平台-收押业务-生物特征信息
  app_biometricInfoCreate : acpCom + '/acp/db/biometricInfo/create',
  // 根据人员编号查询生物特征信息
  app_getBiometricInfoByRybh: acpCom + '/acp/db/biometricInfo/getBiometricInfoByRybh',
  // 更新所领导审批状态
  app_inRecordJlsUpdateLeaderApprovalStatus: acpCom + '/acp/db/inRecordJls/updateLeaderApprovalStatus',
  // 获取区域列表
  app_getAreaList: acpCom + '/base/area/list',
  // 获得实战平台-监管管理-区域监室分页
  app_getAreaPrisonRoomPage: acpCom + '/base/pm/areaPrisonRoom/page',
  // 收回登记
  app_detainReclaimCreate: acpCom + '/acp/db/detainReclaim/create',
  // 通过人员编号查询收回登记信息
  app_detainReclaimGetByJgrybm: acpCom + '/acp/db/detainReclaim/getByJgrybm',
  // 人员选择组件详情查询接口，通过监管人员编号查询
  app_getPrisonerSelectCompomenOne: acpCom + '/base/pm/prisoner/getPrisonerSelectCompomenOne',
  // 创建实战平台-羁押业务-出所登记（看守所）
  app_outRecordKssCreate: acpCom + '/acp/db/outRecordKss/create',
  // 获得实战平台-羁押业务-出所登记（看守所）
  app_outRecordKssGet: acpCom + '/acp/db/outRecordKss/get',
  // 获得实战平台-羁押业务-出所登记（看守所）根据人员编号好奇
  app_outRecordKssGetByJgrybm: acpCom + '/acp/db/outRecordKss/getByJgrybm',
  // 通过人员编号获取该人员生物信息采集以及核验信息（一键核验）
  app_outBiometricInfoGetByJgrybm: acpCom + '/acp/db/outBiometricInfo/getByJgrybm',
  // 创建实战平台-羁押业务-转所登记
  app_transferRecordCreate: acpCom + '/acp/db/transferRecord/create',
  // 获得实战平台-羁押业务-转所登记
  app_transferRecordGet: acpCom + '/acp/db/transferRecord/get',
  // 根据人员编号查询该人员的物品信息
  app_getPersonalEffects: acpCom + '/acp/db/outPersonalEffectsSub/getPersonalEffects',
  // 创建实战平台-收押业务-出所随身物品登记子
  app_createOutPersonalEffectsSub: acpCom + '/acp/db/outPersonalEffectsSub/createOutPersonalEffectsSub',
  // 获取各个流程状态信息
  app_inRecordJlsGetInRecordStatus: acpCom + '/acp/db/inRecordJls/getInRecordStatus',
  // 更新流程信息-看守所收押登记
  app_inRecordJlsUpdateWorkflowInfo: acpCom +'/acp/db/inRecordJls/updateWorkflowInfo',

  // 综合业务-通知交办
  // 创建通知交办
  app_notifyAssign_create: acpCom +'/acp/pm/notifyAssign/create',
  // 删除通知交办
  app_notifyAssign_delete: acpCom +'/acp/pm/notifyAssign/delete',
  // 获得通知交办信息
  app_notifyAssign_get: acpCom +'/acp/pm/notifyAssign/get',
  // 信息处理
  app_notifyAssign_dispose: acpCom +'/acp/pm/notifyAssign/dispose',
  // 更新通知交办
  app_notifyAssign_update: acpCom +'/acp/pm/notifyAssign/update',

    // 综合业务-监管知识库
  // 创建监管知识库
  app_knowledgeBase_create: acpCom +'/acp/pm/knowledgeBase/create',
  // 删除监管知识库
  app_knowledgeBase_delete: acpCom +'/acp/pm/knowledgeBase/delete',
  // 获得监管知识库信息
  app_knowledgeBase_get: acpCom +'/acp/pm/knowledgeBase/get',
  // 更新监管知识库
  app_knowledgeBase_update: acpCom +'/acp/pm/knowledgeBase/update',

  // 收押业务-留所服刑
  // 实战平台-收押业务-创建留所服刑
  app_serveSentence_create: acpCom +'/acp/db/serveSentence/create',
  // 实战平台-收押业务-获得留所服刑
  app_serveSentence_get: acpCom +'/acp/db/serveSentence/get',
  // 实战平台-收押业务-审批流程
  app_serveSentence_approve: acpCom +'/acp/db/serveSentence/approve',
}
