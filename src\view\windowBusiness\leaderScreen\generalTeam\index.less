.generalTeam {
    width: 94%;
    display: flex;
    justify-content: space-between;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 30px;

    .subtitle {
        /* width: 95%;*/
        height: 18px;
        background: url('~@/assets/images/leaderScreen/xbt.png');
        background-size: 100% 100%;
        font-family: <PERSON>YaHei, MicrosoftYaHei;
        font-weight: normal;
        font-size: 16px;
        line-height: 14px;
        color: #CDFBFE;
        padding-left: 22px;
    }
}

.left-box {
    width: 436px;
    height: 944px;
    position: relative;
    background: linear-gradient(90deg, rgba(2, 26, 48, 0.2) 0%, rgba(12, 54, 91, 0.8) 49%, rgba(2, 26, 48, 0.3) 100%);

    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: -1;
        height: 1px;
        width: 100%;
        background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
        /* 渐变色 */
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
        height: 1px;
        width: 100%;
        background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
        /* 渐变色 */
    }

    .com-title {
        width: 298px;
        height: 36px;
        background: url('~@/assets/images/leaderScreen/zbt.png');
        background-size: 100% 100%;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: bold;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 26px;
        padding-left: 40px;
    }

    .content {
        padding: 16px;

        .nowNum {
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            border-bottom: 1px dashed rgb(130, 160, 174, 0.5);

            .jyzs-img {
                width: 42px;
                height: 42px;
            }

            .title {
                font-size: 16px;
                color: #CDFBFE;
                margin-left: 8px;
            }

            .num-list {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-left: 24px;

                .item {
                    width: 24px;
                    height: 30px;
                    border: 1px solid #2C548B;
                    font-size: 18px;
                    color: #00EAFF;
                    text-align: center;
                }
            }

            .yesterday {
                font-size: 12px;
                color: #8CAFBF;
                margin-left: 10px;

                span {
                    color: #EA545F;
                }
            }

        }

        .house-list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
            margin-bottom: 20px;

            .item {
                width: 94px;
                height: 184px;
                background: url('~@/assets/images/leaderScreen/nrk.png');
                background-size: 100% 100%;
                display: flex;
                flex-direction: column;
                align-items: center;

                .title {
                    font-size: 14px;
                    color: #CDFBFE;
                    text-align: center;
                    margin-top: 2px;
                }

                .num {
                    font-weight: bold;
                    font-size: 20px;
                    color: #00EAFF;
                    margin-top: 10px;
                }

                .compare {
                    font-size: 12px;
                    color: #8CAFBF;


                    .add {
                        color: #EA545F;

                    }

                    .del {
                        color: #15E66F;
                    }
                }

                .circle {
                    width: 62px;
                    height: 62px;
                    border: 6px solid #3272A330;
                    border-radius: 50%;
                    margin-top: 10px;

                    .demo-Circle-inner {
                        .percent {
                            font-family: Arial, Arial;
                            font-weight: 400;
                            font-size: 16px;
                            color: #FBFDFD;

                            span {
                                font-size: 8px;
                            }
                        }
                    }
                }

                .text {
                    font-weight: normal;
                    font-size: 12px;
                    color: #FFFFFF;
                }
            }

        }

        .charts {
            .myChartBoxA {
                width: 100%;
                height: 230px;
            }

            .myChartBoxB {
                width: 100%;
                height: 230px;
            }
        }

        .detention {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20px;

            .left {
                display: flex;
                align-items: center;
                width: 277px;
                height: 52px;
                background: linear-gradient(270deg, rgba(42, 73, 103, 0) 0%, rgba(31, 125, 170, 0.3) 100%);
                border-left: 2px solid #029CD8;
                padding: 0 22px;

                .title {
                    font-weight: normal;
                    font-size: 16px;
                    color: #FBFDFD;
                    text-shadow: 0px 20px 50px rgba(0, 0, 0, 0.5);
                }

                .port {
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: bold;
                    font-size: 22px;
                    color: #FADB91;
                    // letter-spacing: 50px;
                    margin-left: 48px;
                    text-shadow: 0px 0px 15px rgba(250, 219, 145, 0.5);
                }
            }

            .jyb-img {
                width: 90px;
                height: 90px;
            }
        }

    }
}


.right-box {
    width: 436px;
    height: 956px;
    position: relative;

    .com-title {
        margin-left: auto;
        background: url('~@/assets/images/leaderScreen/ybt.png');
        background-size: 100% 100%;
        width: 298px;
        height: 36px;
        text-align: end;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: bold;
        font-size: 16px;
        color: #FFFFFF;
        padding-right: 38px;
        line-height: 30px;
    }

    .top {
        position: relative;
        background: linear-gradient(270deg, rgba(2, 26, 48, 0.2) 0%, rgba(12, 54, 91, 0.8) 49%, rgba(2, 26, 48, 0.3) 100%);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: -1;
            height: 1px;
            width: 100%;
            background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
            /* 渐变色 */
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            height: 1px;
            width: 100%;
            background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
            /* 渐变色 */
        }

        .flex-box {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 0 20px;
            border-bottom: 1px dashed #8CAFBF30;

            .mid-img {
                width: 149px;
                height: 178px;
            }

            .item {
                .title {
                    font-size: 14px;
                    color: #D6F6FF;
                    width: 70px;
                    height: 20px;

                    padding-right: 15px;
                    background: linear-gradient(90deg, rgba(0, 178, 255, 0) 0%, rgba(0, 133, 255, 0.27) 74%, rgba(0, 117, 255, 0) 100%);
                }

                .num {
                    font-weight: bold;
                    font-size: 20px;
                    color: #FFFFFF;

                    span {
                        font-weight: normal;
                        font-size: 12px;
                        color: #8CAFBF;
                        margin-left: 5px;
                    }
                }

            }

            .left {
                width: 75px;

                .title {
                    text-align: right;
                }

                .num {
                    text-align: right;
                }

                .line {
                    margin: 8px 0;
                    width: 120px;
                    height: 1px;
                    background: linear-gradient(90deg, rgba(130, 192, 254, 0) 0%, #65E0F8 100%);
                }
            }

            .right {
                width: 75px;

                .line {
                    position: relative;
                    left: -30px;
                    margin: 8px 0;
                    width: 120px;
                    height: 1px;
                    background: linear-gradient(90deg, #65E0F8 0%, rgba(130, 192, 254, 0) 100%);
                }

            }

        }

        .charts {
            padding: 10px 15px;

            .myChartBoxD {
                width: 100%;
                height: 145px;
            }
        }

    }

    .mid {
        position: relative;
        margin-top: 17px;
        background: linear-gradient(270deg, rgba(2, 26, 48, 0.2) 0%, rgba(12, 54, 91, 0.8) 49%, rgba(2, 26, 48, 0.3) 100%);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: -1;
            height: 1px;
            width: 100%;
            background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
            /* 渐变色 */
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            height: 1px;
            width: 100%;
            background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
            /* 渐变色 */
        }

        .charts {
            .myChartBoxE {
                width: 100%;
                height: 197px;
            }

        }

    }

    .btm {
        position: relative;
        margin-top: 17px;
        background: linear-gradient(270deg, rgba(2, 26, 48, 0.2) 0%, rgba(12, 54, 91, 0.8) 49%, rgba(2, 26, 48, 0.3) 100%);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: -1;
            height: 1px;
            width: 100%;
            background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
            /* 渐变色 */
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            height: 1px;
            width: 100%;
            background: linear-gradient(91deg, rgba(0, 178, 255, 0.05) 0%, rgba(89, 201, 255, 0.49) 50%, rgba(0, 117, 255, 0.05) 100%);
            /* 渐变色 */
        }
        .newly-box {
            padding: 10px 20px;
        }

        .newly {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            .day {
                display: flex;
                align-items: center;
                gap: 8px;

                .day-ye {
                    font-size: 12px;
                    color: #8CAFBF;

                    .add {
                        color: #EA545F;
                    }
                }
            }
            .year {
                display: flex;
                align-items: center;
                gap: 8px;
                padding-right: 15px;
            }

            .com-img {
                width: 42px;
                height: 42px;
            }

            .com-sy {
                font-size: 14px;
                color: #CDFBFE;
            }

            .com-num {
                font-size: 18px;
                color: #00EAFF;
            }
        }

        .myChartBoxC {
            width: 100%;
            height: 172px;
        }
    }

}

.center-box {
    flex-grow: 1;
    .mapChart {
        width: 900px;
        height: 100%;
        margin: auto;
    }
}