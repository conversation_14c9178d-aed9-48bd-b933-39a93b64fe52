<template>
  <div style="width: 100%; height: 100%;">
    <div class="table-container" v-if="specialMealContainera">
      <s-DataGrid ref="grid" funcMark="jspctscsqlb" :customFunc="true" :params="params">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':jspctscsqlb:add')"
            @click.native="handleAddESpecialMeal('add')">申请特殊餐/病号餐
          </Button>
          <Button type="success" icon="ios-add" v-if="func.includes(globalAppCode + ':jspctscsqlb:add')"
            @click.native="handleBatchAddSpecialMeal" style="margin-left: 10px;">批量申请特殊餐
          </Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="primary"
            v-if="func.includes(globalAppCode + ':jspctscsqlb:audit') && row.reg_status === '01' && row.meal_type === '07' && !row.doctor_approval_result"
            @click.native="handleAudit(index, row, 'audit')">审核
          </Button>
          <Button type="primary"
            v-if="func.includes(globalAppCode + ':jspctscsqlb:ledaudit') && row.reg_status === '01'"
            @click.native="handleAudit(index, row, 'ledaudit')">审核
          </Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':jspctscsqlb:qr') && row.reg_status === '00'"
            @click.native="handleQr(index, row, 'qr')">确认
          </Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':jspctscsqlb:detail')"
            @click.native="handleDetail(index, row)">详情
          </Button>
        </template>
      </s-DataGrid>
    </div>

    <div class="add-special-meal" v-if="approvalSelectPeople">
      <div class="header-special-meal">
        <!-- <header>
            <h4>特殊餐申请</h4>
        </header> -->
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />
          特殊餐申请
        </p>
        <div class="people-info">
          <div class="upload-img">
            <div class="add-svg" @click="openModal = true">
              <img src="../../../../assets/images/cateringManage/add_people.svg" alt="">
              <span>选择人员</span>
            </div>
            <div class="front-Photo">
              <img :src="ryxxInfo.frontPhoto ? ryxxInfo.frontPhoto : personImg" alt="">
            </div>
          </div>
          <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
            title="人员列表">
            <div class="select-use">
              <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS" :isMultiple="false"
                :selectUseIds="selectUseIds" />
            </div>
            <div slot="footer">
              <Button type="primary" @click="useSelect" class="save">确 定</Button>
              <Button @click="openModal = false" class="save">关 闭</Button>
            </div>
          </Modal>
          <div class="people-msg">
            <Row style="margin-bottom: 1px;">
              <Col span="8">
              <span class="Light-blue">
                被监管人员
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.xm }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">
                监室号
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.roomName }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">
                入所时间
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.rssj }}
              </span>
              </Col>
            </Row>
            <Row style="margin-bottom: 1px;">
              <Col span="8">
              <span class="Light-blue">
                诉讼环节
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.sshjName }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">
                关押期限
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.gyqx }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">
                涉嫌罪名
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.sxzmName }}
              </span>
              </Col>
            </Row>
            <Row style="margin-bottom: 1px;">
              <Col span="8">
              <span class="Light-blue">
                风险等级
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.fxdjName }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">
                户籍地
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.hjdName }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">

              </span>
              <span class="ligth-gray">

              </span>
              </Col>
            </Row>
            <Row style="margin-bottom: 1px;">
              <Col span="8">
              <span class="Light-blue">
                近期就诊时间
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.jqjzsj }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">
                诊断病情
              </span>
              <span class="ligth-gray">
                {{ ryxxInfo.zjzdbq }}
              </span>
              </Col>
              <Col span="8">
              <span class="Light-blue">

              </span>
              <span class="ligth-gray">

              </span>
              </Col>
            </Row>
          </div>
        </div>
      </div>
      <div class="content-add-form">
        <div class="form-container fm-content-wrap" style="padding: 0;">
          <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="165">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              配餐设置
            </p>
            <Row>
              <Col span="10">
              <!-- <FormItem label="配餐类型" prop="mealType">
                    <s-dicgrid v-model="formValidate.mealType" dicName="ZD_PCGL_PCLX" />
                </FormItem> -->
              <FormItem label="配餐类型" prop="mealType"
                :rules="[{ trigger: 'blur,change', message: '请选择配餐类型', required: true }]" style="width: 100%;">
                <s-dicgrid v-model="formValidate.mealType" dicName="ZD_PCGL_PCLX" />
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="配餐时间" prop="time">
                <DatePicker :transfer="true" v-model="formValidate.time" type="datetimerange" format="yyyy-MM-dd"
                  placeholder="请选择时间段" />
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="用餐时段" prop="mealPeriodType">
                <CheckboxGroup v-model="formValidate.mealPeriodType">
                  <Checkbox v-for="item in useMealList" :label="item.code" :key="item.code">{{
                    item.name
                  }}
                  </Checkbox>
                </CheckboxGroup>
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="指定日期" prop="specifiedDateType">
                <div class="radio-checkbox">
                  <RadioGroup v-model="formValidate.specifiedDateType">
                    <Radio v-for="item in useTimeTypeList" :label="item.code" :key="item.code">{{
                      item.name
                    }}
                    </Radio>
                  </RadioGroup>
                  <CheckboxGroup v-model="formValidate.specifiedDateTime">
                    <Checkbox v-for="item in useWeeksList" :label="item.code" :key="item.code">{{
                      item.name
                    }}
                    </Checkbox>
                  </CheckboxGroup>
                </div>
              </FormItem>
              </Col>
              <Col span="20" style="margin-top: 20px;">
              <FormItem label="申请原因" prop="reason">
                <Input v-model="formValidate.reason" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请输入申请原因"></Input>
              </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="bsp-base-fotter">
          <Button @click="handleReset('formValidate')" style="margin-left: 8px">取消</Button>
          <Button style="margin-left: 10px;" type="primary" @click="handleSubmit('formValidate')">确认</Button>
        </div>
      </div>
    </div>
    <div v-if="approvalContainer">
      <div class="fm-content-info">
        <Form ref="formData" inline>
          <div class="fm-content-box">
            <p class="fm-content-info-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              个人信息
            </p>
            <Row>
              <Col span="3" class="col-title"><span>人员图片</span></Col>
              <Col span="21">
              <span>
                <div class="front-Photo">
                  <img :src="ryxxInfo.frontPhoto ? ryxxInfo.frontPhoto : personImg" alt="">
                </div>
              </span>
              </Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>被监管人员</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.bm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>监室号</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.jsh }}
              </span></Col>
              <Col span="3" class="col-title"><span>入所时间</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.rssj }}
              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>诉讼环节</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.sshj }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>关押期限</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.gyqx }}
              </span></Col>
              <Col span="3" class="col-title"><span>涉嫌罪名</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.sxzm }}
              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>风险等级</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.fxdj }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>户籍地</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.hjd }}
              </span></Col>
              <Col span="3" class="col-title"><span></span></Col>
              <Col span="5"><span>

              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>近期就诊时间</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.jqjzsj }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>诊断病情</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.zjzdbq }}
              </span></Col>
              <Col span="3" class="col-title"><span></span></Col>
              <Col span="5"><span>

              </span></Col>
            </Row>

          </div>
        </Form>
      </div>


      <div class="fm-content-info">
        <Form ref="formData" inline>
          <div class="fm-content-box">
            <p class="fm-content-info-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              申请登记
            </p>
            <Row>
              <Col span="3" class="col-title"><span>配餐类型</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.mealTypeName }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>监室号</span></Col>
              <Col span="9"><span>
                {{ ryxxInfo.jsh }}
              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>用餐时段</span></Col>
              <Col span="21"><span>
                {{ specialApplydetail.mealPeriodName }}
              </span>
              </Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>指定日期</span></Col>
              <Col span="21"><span>
                {{ specialApplydetail.specifiedDateName }}
              </span>
              </Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>申请原因</span></Col>
              <Col span="21"><span>
                {{ specialApplydetail.reason }}
              </span>
              </Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>申请人</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.regOperatorXm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>申请时间</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.regTime }}
              </span>
              </Col>
            </Row>

          </div>
        </Form>
      </div>

      <div class="fm-content-info" v-if="specialApplydetail.leaderApprovalResult != 0">
        <Form ref="formData" inline>
          <div class="fm-content-box">
            <p class="fm-content-info-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              医生审批意见
            </p>
            <Row>
              <Col span="3" class="col-title"><span>审批结果</span></Col>
              <Col span="9"><span>
                {{
                  specialApplydetail.doctorApprovalResult ? specialApplydetail.doctorApprovalResult === '1' ?
                    "同意" : "不同意" : ""
                }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>审批意见</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.doctorApprovalComments }}
              </span></Col>
            </Row>


            <Row>
              <Col span="3" class="col-title"><span>审批人</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.doctorApproverXm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>审批时间</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.doctorApproverTime }}
              </span></Col>
            </Row>
          </div>
        </Form>
      </div>

      <div class="fm-content-info">
        <Form ref="formData" inline style="margin-bottom: 30px;">
          <div class="fm-content-box">
            <p class="fm-content-info-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              所领导审批意见
            </p>
            <Row>
              <Col span="3" class="col-title"><span>审批结果</span></Col>
              <Col span="9"><span>
                {{
                  specialApplydetail.leaderApprovalResult ? specialApplydetail.leaderApprovalResult === '1' ?
                    "同意" : "不同意" : ""
                }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>审批意见</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.leaderApprovalComments }}
              </span></Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>审批人</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.leaderApproverXm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>审批时间</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.leaderApproverTime }}
              </span></Col>
            </Row>
          </div>
        </Form>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleCaneclDetail" style="margin-right: 10px;">取消</Button>
      </div>
    </div>
    <div v-if="auditWithDetailContainer">
      <div class="fm-content-info">
        <Form ref="formData" inline>
          <div class="fm-content-box">
            <p class="fm-content-info-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              个人信息
            </p>
            <Row>
              <Col span="3" class="col-title"><span>人员图片</span></Col>
              <Col span="21">
              <span>
                <div class="front-Photo">
                  <img :src="ryxxInfo.frontPhoto ? ryxxInfo.frontPhoto : personImg" alt="">
                </div>
              </span>
              </Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>被监管人员</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.bm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>监室号</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.jsh }}
              </span></Col>
              <Col span="3" class="col-title"><span>入所时间</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.rssj }}
              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>诉讼环节</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.sshj }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>关押期限</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.gyqx }}
              </span></Col>
              <Col span="3" class="col-title"><span>涉嫌罪名</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.sxzm }}
              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>风险等级</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.fxdj }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>户籍地</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.hjd }}
              </span></Col>
              <Col span="3" class="col-title"><span></span></Col>
              <Col span="5"><span>

              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>近期就诊时间</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.jqjzsj }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>诊断病情</span></Col>
              <Col span="5"><span>
                {{ ryxxInfo.zjzdbq }}
              </span></Col>
              <Col span="3" class="col-title"><span></span></Col>
              <Col span="5"><span>

              </span></Col>
            </Row>

          </div>
        </Form>
      </div>
      <div class="fm-content-info">
        <Form ref="formData" inline>
          <div class="fm-content-box">
            <p class="fm-content-info-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              申请登记
            </p>
            <Row>
              <Col span="3" class="col-title"><span>配餐类型</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.mealTypeName }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>监室号</span></Col>
              <Col span="9"><span>
                {{ ryxxInfo.jsh }}
              </span></Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>用餐时段</span></Col>
              <Col span="21"><span>
                {{ specialApplydetail.mealPeriodName }}
              </span>
              </Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>指定日期</span></Col>
              <Col span="21"><span>
                {{ specialApplydetail.specifiedDateName }}
              </span>
              </Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>申请原因</span></Col>
              <Col span="21"><span>
                {{ specialApplydetail.reason }}
              </span>
              </Col>
            </Row>

            <Row>
              <Col span="3" class="col-title"><span>申请人</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.regOperatorXm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>申请时间</span></Col>
              <Col span="9"><span>
                {{ specialApplydetail.regTime }}
              </span>
              </Col>
            </Row>

          </div>
        </Form>
      </div>

      <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0;" v-if="auditType == 'audit'">
        <Form ref="formItem" :model="formItem" :label-width="150" style="width: 100%;">
          <div class="fm-content-form">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              医生审批意见
            </p>
            <Row style="padding: 10px;">
              <Col span="12">
              <FormItem label="审批结果" prop="doctorApprovalResult"
                :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }">
                <RadioGroup v-model="formItem.doctorApprovalResult">
                  <Radio label="1">同意</Radio>
                  <Radio label="0">不同意</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="审批意见" prop="doctorApprovalComments"
                :rules="{ required: true, message: '请输入审批意见', trigger: 'change' }">
                <Input v-model="formItem.doctorApprovalComments" type="textarea" style="width: 300px;"
                  :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入审批意见"></Input>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
              <FormItem label="审批人">
                <Input v-model="doctorApproveName" :disabled="true" placeholder="审批人"></Input>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="审批时间">
                ---
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>

      <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0;" v-if="auditType == 'ledaudit'">
        <Form ref="formLedItem" :model="formLedItem" :label-width="150" style="width: 100%;margin-bottom: 30px;">
          <div class="fm-content-form">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              所领导审批意见
            </p>
            <Row style="padding: 10px;">
              <Col span="12">
              <FormItem label="审批结果" prop="leaderApprovalResult"
                :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }">
                <RadioGroup v-model="formLedItem.leaderApprovalResult">
                  <Radio label="1">同意</Radio>
                  <Radio label="0">不同意</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="审批意见" prop="leaderApprovalComments"
                :rules="{ required: true, message: '请输入审批意见', trigger: 'change' }">
                <Input v-model="formLedItem.leaderApprovalComments" type="textarea" style="width: 300px;"
                  :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入审批意见"></Input>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
              <FormItem label="审批人">
                <Input v-model="doctorApproveName" :disabled="true" placeholder="审批人"></Input>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="审批时间">
                ---
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>

      <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0;" v-if="auditType == 'qr'">
        <Form ref="formqrData" :model="qrData" :label-width="150" style="width: 100%;">
          <div class="fm-content-form">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />
              管教确认
            </p>
            <Row style="padding: 10px;">
              <Col span="12">
              <FormItem label="审批结果" prop="success">
                <RadioGroup v-model="qrData.success">
                  <Radio label="1">同意</Radio>
                  <Radio label="0">不同意</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>

      <div class="bsp-base-fotter" v-if="auditType == 'audit'">
        <Button size="large" @click="handleResetAuditApproval('formItem')">取消</Button>
        <Button size="large" type="primary" style="margin-left: 10px;" @click="handleAuditApprovalSubmit('formItem')">确认
        </Button>
      </div>

      <div class="bsp-base-fotter" v-if="auditType == 'ledaudit'">
        <Button size="large" @click="handleResetAuditApproval('formLedItem')">取消</Button>
        <Button size="large" type="primary" style="margin-left: 10px;" :loading="loading"
          @click="handleAuditApprovalSubmit('formLedItem')">确认
        </Button>
      </div>
      <div class="bsp-base-fotter" v-if="auditType === 'qr'">
        <Button size="large" @click="handleResetAuditApproval('formqrData')">取消</Button>
        <Button size="large" type="primary" style="margin-left: 10px;" :loading="loading" @click="handleQrReq()">确认
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import { prisonSelect } from 'sd-prison-select'
import dayjs from 'dayjs'
import Cookies from 'js-cookie'

export default {
  name: "specialMealRequest",
  data() {
    return {
      params: {},
      loading: false,
      qrData: {},
      specialMealContainera: true,
      approvalSelectPeople: false,
      auditWithDetailContainer: false,
      formValidate: {
        mealType: "",
        time: "",
        roomId: "",
        mealPeriodType: [],
        specifiedDateType: "",
        specifiedDateTime: [],
        reason: ""
      },
      ruleValidate: {
        // mealType: [
        //     { trigger: 'blur, change', message: '配餐类型不能为空', required: true}
        // ],
        time: [
          { required: true, type: 'array', message: '配餐时间不能为空', trigger: 'blur' },
        ],
        mealPeriodType: [
          { required: true, type: 'array', min: 1, message: '至少选择一个用餐时段', trigger: 'change' },
        ],
        specifiedDateType: [
          { required: true, message: '请选择指定日期', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入申请原因', trigger: 'blur' },
          { type: 'string', min: 1, message: '申请原因不能少于1个字', trigger: 'blur' }
        ]
      },
      mealTypeList: [],
      useMealList: [],
      useTimeTypeList: [],
      useWeeksList: [
        {
          name: "周一",
          code: '1'
        },
        {
          name: "周二",
          code: '2'
        },
        {
          name: "周三",
          code: '3'
        },
        {
          name: "周四",
          code: '4'
        },
        {
          name: "周五",
          code: '5'
        },
        {
          name: "周六",
          code: '6'
        },
        {
          name: "周日",
          code: '7'
        }
      ],
      openModal: false,
      ryxxObj: {},
      selectUseIds: "",
      ryxxInfo: {},
      specialApplydetail: {},
      personImg: require("../../../../assets/images/cateringManage/default_Img.svg"),
      approvalContainer: false,
      formItem: {
        doctorApprovalResult: "1",
        doctorApprovalComments: "",
      },
      formLedItem: {
        leaderApprovalResult: "1",
        leaderApprovalComments: "",
      },
      auditType: "",
      id: "",
    }

  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleAddESpecialMeal() {
      this.specialMealContainera = false
      this.approvalSelectPeople = true
      this.ryxxInfo = {}
    },
    // 批量申请特殊餐
    handleBatchAddSpecialMeal() {
      this.$router.push({ name: "specialMealRequestCreate" });
    },
    // 审核
    handleAudit(index, { id, jgrybm }, name) {
      this.id = id
      this.auditType = name
      this.handleGetRyxxInfo(jgrybm)
      this.handleGetSpecialApplydetail(id)
      this.auditWithDetailContainer = true
      this.specialMealContainera = false
    },
    handleResetAuditApproval(name) {
      this.$refs[name].resetFields();
      this.auditWithDetailContainer = false
      this.specialMealContainera = true
    },
    handleAuditApprovalSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let url = ""
          let params = {}
          if (name == 'formItem') {
            url = this.$path.specialApply_doctorApprove
            params = this.formItem
          } else {
            url = this.$path.specialApply_leaderApprove
            params = this.formLedItem
          }
          params.id = this.id
          this.loading = true
          this.authPostRequest({ url: url, params: params }).then(res => {
            if (res.success) {
              this.auditWithDetailContainer = false
              this.specialMealContainera = true
              this.formLedItem.leaderApprovalComments = ""
              this.formItem.doctorApprovalComments = ""
              this.$Message.success('审核成功')
            } else {
              this.$Message.error(res.message)
            }
            this.loading = false
          })
        } else {
          this.$Message.error('验证失败');
        }
      })
    },
    handleGetSpecialApplydetail(id) {
      this.authGetRequest({ url: this.$path.specialApply_people_detail, params: { id: id } }).then(res => {
        if (res.success) {
          this.specialApplydetail = res.data
          console.error(res.data, '[res.data]');

          let mealPeriod = this.specialApplydetail.mealPeriod.split(',')
          let specifiedDate = this.specialApplydetail.specifiedDate.split(',')
          this.specialApplydetail.doctorApprovalResult
          let mealPeriodName = mealPeriod.map(item => {
            if (item == 0) {
              item = '早餐'
            } else if (item == 1) {
              item = '午餐'
            } else {
              item = "晚餐"
            }
            return item
          })
          let specifiedDateName = specifiedDate.map(item => {
            if (item == 1) {
              item = '星期一'
            } else if (item == 2) {
              item = '星期二'
            } else if (item == 3) {
              item = '星期三'
            } else if (item == 4) {
              item = '星期四'
            } else if (item == 5) {
              item = '星期五'
            } else if (item == 6) {
              item = '星期六'
            } else {
              item = '星期日'
            }
            return item
          })

          let mealTypeName = ""
          switch (this.specialApplydetail.mealType) {
            case "00":
              mealTypeName = '普通餐'
              break;
            case "01":
              mealTypeName = '清真餐'
              break;
            case "02":
              mealTypeName = '生日餐'
              break;
            case "03":
              mealTypeName = '节日餐'
              break;
            case "04":
              mealTypeName = '宗教餐'
              break;
            case "05":
              mealTypeName = '低糖餐'
              break;
            case "06":
              mealTypeName = '老年餐'
              break;
            case "07":
              mealTypeName = '病号餐'
              break;
            default:
              mealTypeName = '其他餐'
              break;
          }
          this.specialApplydetail['mealTypeName'] = mealTypeName
          this.specialApplydetail['mealPeriodName'] = mealPeriodName.join(',')
          this.specialApplydetail['specifiedDateName'] = specifiedDateName.join(',')
          const startDate = dayjs(this.specialApplydetail.mealStartTime);
          const endDate = dayjs(this.specialApplydetail.mealEndTime);
          const daysDiff = endDate.diff(startDate, 'day');
          this.specialApplydetail['daysDiff'] = daysDiff
        }
      })
    },
    handleQr(index, { id, jgrybm }, name) {
      this.id = id
      this.auditType = name
      this.handleGetRyxxInfo(jgrybm)
      this.handleGetSpecialApplydetail(id)
      this.auditWithDetailContainer = true
      this.specialMealContainera = false
    },
    handleQrReq() {
      if (!this.qrData.success) {
        this.$Message.error('请选择审批结果')
        return
      }
      this.loading = true
      this.authPostRequest({
        url: this.$path.specialApply_confirm,
        params: {
          id: this.id,
          success: this.qrData.success
        }
      }).then(res => {
        if (res.success) {
          this.auditWithDetailContainer = false
          this.specialMealContainera = true
          this.$Message.success('操作成功')
        } else {
          this.$Message.error(res.msg || '保存失败！')
        }
        this.loading = false
      })
    },
    // 详情
    handleDetail(index, { id, jgrybm }) {
      this.handleGetRyxxInfo(jgrybm)
      this.handleGetSpecialApplydetail(id)
      this.approvalContainer = true
      this.specialMealContainera = false
    },
    handleCaneclDetail() {
      this.approvalContainer = false
      this.specialMealContainera = true
    },
    useSelect() {
      this.ryxxObj = this.$refs.prisonSelect.checkedUse[0]//{ ...this.formData, ...this.$refs.prisonSelect.checkedUse[0] }
      this.selectUseIds = this.ryxxObj.jgrybm
      this.handleGetRyxxInfo(this.selectUseIds)
      this.openModal = false
    },
    handleGetRyxxInfo(selectUseIds) {
      this.authGetRequest({ url: this.$path.specialApply_people_search, params: { jgrybm: selectUseIds } }).then(res => {
        if (res.success) {
          this.ryxxInfo = res.data
        }
      })
    },
    // 配餐类型
    handleGetZD_PCGL_PCLX() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_PCLX.js" }).then(res => {
        let mealType = eval('(' + res + ')')
        this.mealTypeList = mealType()
        console.error(this.mealTypeList);
      })
    },
    // 获取用餐时段字段
    handleGetZD_PCGL_DSLX() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_DSLX.js" }).then(res => {
        let useMeal = eval('(' + res + ')')
        this.useMealList = useMeal()
      })
    },
    // 指定日期类型
    handleGetZD_PCGL_ZDRQLX() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_ZDRQLX.js" }).then(res => {
        let useTime = eval('(' + res + ')')
        this.useTimeTypeList = useTime()
      })
    },
    handleSubmit(name) {
      if (this.selectUseIds && this.ryxxInfo.jsh) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            let mealStartTime = dayjs(this.formValidate.time[0]).format('YYYY-MM-DD')
            let mealEndTime = dayjs(this.formValidate.time[1]).format('YYYY-MM-DD')
            this.formValidate.mealStartTime = mealStartTime
            this.formValidate.mealEndTime = mealEndTime
            this.formValidate.mealPeriod = this.formValidate.mealPeriodType.join(',')
            this.formValidate.specifiedDate = this.formValidate.specifiedDateTime.join(',')
            this.formValidate.jgrybm = this.selectUseIds
            this.formValidate['roomId'] = this.ryxxInfo.jsh


            this.authPostRequest({ url: this.$path.specialApply_create, params: this.formValidate }).then(res => {
              if (res.success) {
                this.$Message.success('特殊餐申请成功')
                this.specialMealContainera = true
                this.approvalSelectPeople = false
                this.$refs[name].resetFields();
                this.formValidate.mealType = ""
                this.formValidate.time = ""
                this.formValidate.mealPeriodType = []
                this.formValidate.specifiedDateType = ""
                this.formValidate.specifiedDateTime = []
                this.formValidate.reason = ""
              } else {
                this.$Message.error(res.message);
              }
            })

          } else {
            this.$Message.error('验证失败');
          }
        })
      } else {
        this.$Message.error('请选择人员后进行提交');
      }

    },
    handleReset(name) {
      this.$refs[name].resetFields();
      this.specifiedDate = []
      this.specialMealContainera = true
      this.approvalSelectPeople = false
      this.formValidate.mealType = ""
      this.formValidate.time = ""
      this.formValidate.mealPeriodType = []
      this.formValidate.specifiedDateType = ""
      this.formValidate.specifiedDateTime = []
      this.formValidate.reason = ""
      this.ryxxInfo = {}
    },

  },

  components: {
    sDataGrid,
    prisonSelect
  },

  created() {
    this.handleGetZD_PCGL_DSLX()
    this.handleGetZD_PCGL_ZDRQLX()
    this.handleGetZD_PCGL_PCLX()
    this.doctorApproveName = Cookies.get('bsp_bus_user_login')
  },

  computed: {},
  watch: {
    'formValidate.specifiedDateType': {
      handler(newV) {
        if (newV == 1) {
          this.formValidate.specifiedDateTime = ['1', '2', '3', '4', '5', '6', '7']
        } else {
          this.formValidate.specifiedDateTime = ['1', '2', '3', '4', '5',]
        }
      }
    },
    'formItem.doctorApprovalResult': {
      handler(newVal) {
        if (newVal == 1) {
          this.formItem.doctorApprovalComments = "同意"
        } else {
          this.formItem.doctorApprovalComments = "不同意"
        }
      },
      immediate: true
    },
    'formLedItem.leaderApprovalResult': {
      handler(newVal) {
        if (newVal == 1) {
          this.formLedItem.leaderApprovalComments = "同意"
        } else {
          this.formLedItem.leaderApprovalComments = "不同意"
        }
      },
      immediate: true
    }
  }

}

</script>

<style scoped lang="less">
.front-Photo {
  padding-top: 5px;

  img {
    width: 143px;
    height: 136px;
  }
}

@import "~@/assets/style/formInfo.css";

/deep/ .fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #00244A;
  /* margin-bottom: 16px; */
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-bottom: 1px solid #CEE0F0;
}

.table-container {
  padding: 10px 15px;
  height: 95%;
}

.add-special-meal {
  width: 100%;
  height: 100%;

  .header-special-meal {
    height: 260px;
    width: 100%;
    background-color: #fff;
    border: 1px solid #cee0f0;

    header {
      height: 50px;
      line-height: 50px;
      border-bottom: solid 1px #f5f5f5;
    }

    .people-info {
      display: flex;
      align-items: flex-end;
      padding-top: 10px;
      margin-right: 16px;

      .upload-img {
        width: 167px;
        height: 165px;
        padding: 0 5px 0 10px;

        .add-svg {
          display: flex;
          align-items: flex-end;
          cursor: pointer;

          img {
            width: 34px;
            height: 24px;
          }

          span {
            display: inline-block;
            color: #2B5FD9;
            margin-left: 5px;
          }
        }

        .front-Photo {
          padding-top: 5px;

          img {
            width: 143px;
            height: 136px;
          }
        }
      }

      .people-msg {
        flex: 1;
      }
    }
  }

  .content-add-form {
    // margin-top: 10px;
    width: 100%;
    background-color: #fff;

    .form-container {
      padding-top: 30px;

      .radio-checkbox {
        display: flex;
        align-items: center;
      }

    }
  }
}

/deep/ .ivu-col-span-8 {
  display: flex;
}


.Light-blue {
  display: inline-block;
  line-height: 40px;
  padding-left: 5px;
  width: 120px;
  height: 40px;
  flex-shrink: 0;
  background-color: #e4eefc;
  margin: 0 1px;
}

.ligth-gray {
  flex: 1;
  height: 40px;
  line-height: 40px;
  padding-left: 5px;
  background-color: #f5f7fa;
}

.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN;
  font-weight: 700;
  font-size: 16px;
  color: #00244a;
  margin-bottom: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
</style>
