<template>
  <div class="special-meal-create" style="display: flex; flex-direction: column; height: 100%">
    <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
      <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />健康检查登记</p>
      <div class="form">
        <Row style="margin-top: 10px; flex: 1">
          <Col :span="6" style="display: flex; flex-direction: column">
          <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
            <ChoosePeople @selectUser="selectUser" :selectedIds="choosenList.map(item => { return item.jgrybm }).join(',')">
            </ChoosePeople>
            <Table style="margin-top: 15px" :columns="[
              { title: '姓名', key: 'jgryxm' },
              { title: '监室', key: 'roomName' },
              { title: '操作', key: 'action', slot: 'action' },
            ]" :data="choosenList">
              <template slot-scope="{ index }" slot="action">
                <Button type="error" ghost size="small" @click="removeChoosenPeople(index)">删除</Button>
              </template>
            </Table>
          </div>
          </Col>
          <Col :span="18" style="
              border-left: 1px solid #efefef;
              display: flex;
              flex-direction: column;
            ">
          <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
            <div class="form-title" style="
                  text-align: center;
                  background: #f5f7fa;
                  line-height: 2.2;
                  border: 1px solid #d7d7d7;
                  border-top-left-radius: 0.2em;
                  border-top-right-radius: 0.2em;
                ">
              特殊餐申请登记
            </div>
            <div class="form-content" style="
                  flex: 1;
                  padding: 15px;
                  border: 1px solid #d7d7d7;
                  border-top: unset;
                  border-bottom-left-radius: 0.2em;
                  border-bottom-right-radius: 0.2em;
                ">
              <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="120" label-position="left"
                @submit.native.prevent>
                <Row>
                  <Col :span="24">
                  <FormItem label="配餐类型" prop="mealType">
                    <s-dicgrid v-model="formData.mealType" dicName="ZD_PCGL_PCLX" :isSearch="false" :multiple="false" />
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="配餐时间" prop="mealTime">
                    <DatePicker
                      type="daterange"
                      v-model="formData.mealTime"
                      placeholder="选择配餐时间"
                      style="width: 100%"
                      format="yyyy-MM-dd"
                      @on-change="handleMealTimeChange"
                    />
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="用餐时段" prop="mealPeriod">
                    <CheckboxGroup v-model="formData.mealPeriod">
                      <Checkbox v-for="item in mealPeriodOptions" :key="item.code" :label="item.code">
                        {{ item.name }}
                      </Checkbox>
                    </CheckboxGroup>
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="指定日期" prop="specifiedDate">
                    <div style="margin-bottom: 10px;">
                      <s-dicgrid v-model="formData.specifiedDateType" dicName="ZD_PCGL_ZDRQLX" :isSearch="false" :multiple="false" @change="handleSpecifiedDateTypeChange" />
                    </div>
                    <div v-if="formData.specifiedDateType">
                      <DatePicker
                        v-if="formData.specifiedDateType === '1'"
                        type="date"
                        v-model="formData.specifiedDateValue"
                        placeholder="选择指定日期"
                        style="width: 100%"
                        format="yyyy-MM-dd"
                        multiple
                        @on-change="handleSpecifiedDateChange"
                      />
                      <Input
                        v-else-if="formData.specifiedDateType === '2'"
                        v-model="formData.specifiedDateValue"
                        placeholder="请输入自定义日期描述"
                        style="width: 100%"
                      />
                    </div>
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="申请原因" prop="reason">
                    <Input v-model="formData.reason" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"
                      placeholder="请输入申请原因"></Input>
                  </FormItem>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
          </Col>
        </Row>
      
    <div class="bsp-base-fotter">
      <Button size="large" @click="handleCancel">取消</Button>
      <Button size="large" type="primary" style="margin-left: 10px;" :loading="loading" @click="handleSubmit">确认
      </Button>
    </div>
  </div>
</template>

<script>
import { ChoosePeople } from "./components";
import { mapActions } from "vuex";
import dayjs from 'dayjs';

export default {
  components: {
    ChoosePeople,
  },
  data() {
    return {
      formData: {
        mealType: "",
        mealTime: [],
        mealPeriod: [],
        specifiedDateType: "",
        specifiedDateValue: "",
        reason: "",
      },
      choosenList: [],
      mealPeriodOptions: [],
      ruleValidate: {
        mealType: [
          {
            required: true,
            message: "请选择配餐类型",
            trigger: "change",
          },
        ],
        mealTime: [
          {
            required: true,
            type: "array",
            message: "请选择配餐时间",
            trigger: "change",
          },
        ],
        mealPeriod: [
          {
            required: true,
            type: "array",
            message: "请选择用餐时段",
            trigger: "change",
          },
        ],
        reason: [
          {
            required: true,
            message: "请输入申请原因",
            trigger: "blur",
          },
        ],
      },
      loading: false,
    };
  },
  created() {
    this.getMealPeriodOptions();
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    
    // 获取用餐时段选项
    getMealPeriodOptions() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "ZD_PCGL_DSLX" },
      }).then((res) => {
        if (res.success) {
          this.mealPeriodOptions = res.data;
        } else {
          this.$Message.error("获取用餐时段选项失败");
        }
      });
    },
    
    selectUser(data) {
      const uniqueArray = new Set(data.map(item => JSON.stringify(item)));
      const resultArray = Array.from(uniqueArray).map(item => JSON.parse(item));
      this.choosenList = resultArray;
    },
    
    removeChoosenPeople(index) {
      this.choosenList.splice(index, 1);
    },
    
    handleMealTimeChange(dates) {
      this.formData.mealTime = dates;
    },
    
    handleSpecifiedDateTypeChange() {
      this.formData.specifiedDateValue = "";
    },
    
    handleSpecifiedDateChange(dates) {
      this.formData.specifiedDateValue = dates;
    },
    
    handleCancel() {
      this.$refs.form.resetFields();
      this.choosenList = [];
      this.$router.replace({ name: "specialMealRequest" });
    },
    
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid && this.choosenList.length) {
          this.loading = true;
          
          // 构建批量保存的参数
          const batchReqVO = {
            jgrybInfo: this.choosenList.map(item => ({
              jgrybm: item.jgrybm,
              roomId: item.roomId
            })),
            mealType: this.formData.mealType,
            mealStartTime: this.formData.mealTime[0] ? dayjs(this.formData.mealTime[0]).format('YYYY-MM-DD') : '',
            mealEndTime: this.formData.mealTime[1] ? dayjs(this.formData.mealTime[1]).format('YYYY-MM-DD') : '',
            mealPeriod: this.formData.mealPeriod.join(','),
            specifiedDateType: this.formData.specifiedDateType,
            specifiedDate: Array.isArray(this.formData.specifiedDateValue) 
              ? this.formData.specifiedDateValue.map(date => dayjs(date).format('YYYY-MM-DD')).join(',')
              : this.formData.specifiedDateValue,
            reason: this.formData.reason
          };

          this.authPostRequest({
            url: this.$path.specialApply_createBatch,
            params: batchReqVO,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("特殊餐申请成功");
              this.$refs.form.resetFields();
              this.choosenList = [];
              this.$router.replace({ name: "specialMealRequest" });
            } else {
              this.$Message.error(res.msg || "申请失败");
            }
          }).catch(() => {
            this.loading = false;
            this.$Message.error("申请失败");
          });
        } else {
          this.$Message.error(
            "表单验证失败，请确定选择了监管人员与填写了必填表单。"
          );
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.special-meal-create {
  .form-container {
    .form-content {
      .ivu-form-item {
        margin-bottom: 20px;
      }
      
      .ivu-form-item-label {
        font-weight: 500;
        color: #333;
      }
    }
  }
}
</style>
