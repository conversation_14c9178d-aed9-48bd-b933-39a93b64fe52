<template>
    <div class="generalTeam">
        <div class="left-box">
            <div class="title com-title">羁押动态</div>
            <div class="content">
                <div class="nowNum">
                    <img src="~@/assets/images/leaderScreen/jyzs.png" class="jyzs-img" alt="">
                    <div class="title">当前羁押总数</div>
                    <div class="num-list">
                        <div class="item">0</div>
                        <div class="item">1</div>
                        <div class="item">2</div>
                        <div class="item">5</div>
                    </div>
                    <div class="yesterday">
                        较昨日<span>+5</span>
                    </div>
                </div>
                <div class="house-list">
                    <div class="item">
                        <div class="title">看守所</div>
                        <div class="num">2,968</div>
                        <div class="compare">较昨日<span class="add">+5</span></div>
                        <div class="circle">
                            <i-circle :percent="85" :size="50" :stroke-width="14" :trail-width="14"
                                trail-color="#3272A3" :stroke-color="['#0278BC', '#55E2FE']" stroke-linecap="square">
                                <div class="demo-Circle-inner">
                                    <div class="percent">85<span>%</span></div>
                                </div>
                            </i-circle>
                        </div>
                        <div class="text">押容比</div>
                    </div>
                    <div class="item">
                        <div class="title">拘留所</div>
                        <div class="num">1,028</div>
                        <div class="compare">较昨日<span class="del">-3</span></div>
                        <div class="circle">
                            <i-circle :percent="67" :size="50" :stroke-width="14" :trail-width="14"
                                trail-color="#3272A3" :stroke-color="['#0278BC', '#55E2FE']" stroke-linecap="square">
                                <div class="demo-Circle-inner">
                                    <div class="percent">67<span>%</span></div>
                                </div>
                            </i-circle>
                        </div>
                        <div class="text">押容比</div>
                    </div>
                    <div class="item">
                        <div class="title">戒毒所</div>
                        <div class="num">96</div>
                        <div class="compare">较昨日<span class="add">+2</span></div>
                        <div class="circle">
                            <i-circle :percent="46" :size="50" :stroke-width="14" :trail-width="14"
                                trail-color="#3272A3" :stroke-color="['#0278BC', '#55E2FE']" stroke-linecap="square">
                                <div class="demo-Circle-inner">
                                    <div class="percent">46<span>%</span></div>
                                </div>
                            </i-circle>
                        </div>
                        <div class="text">押容比</div>
                    </div>
                    <div class="item">
                        <div class="title">监管医院</div>
                        <div class="num">968</div>
                        <div class="compare">较昨日<span class="del">-4</span></div>
                        <div class="circle">
                            <i-circle :percent="35" :size="50" :stroke-width="14" :trail-width="14"
                                trail-color="#3272A3" :stroke-color="['#0278BC', '#55E2FE']" stroke-linecap="square">
                                <div class="demo-Circle-inner">
                                    <div class="percent">35<span>%</span></div>
                                </div>
                            </i-circle>
                        </div>
                        <div class="text">押容比</div>
                    </div>

                </div>
                <div class="subtitle">各监所押量情况</div>
                <div class="charts">
                    <div id="myChartBoxA" class="myChartBoxA" ref="ChartBoxA"></div>
                </div>
                <div class="subtitle">警押比</div>
                <div class="detention">
                    <div class="left">
                        <div class="title">警押比</div>
                        <div class="port">1.35</div>
                    </div>
                    <img class="jyb-img" src="~@/assets/images/leaderScreen/jyb.png" alt="">
                </div>
                <div class="charts">
                    <div id="myChartBoxB" class="myChartBoxB" ref="ChartBoxB"></div>
                </div>
            </div>
        </div>
        <div class="center-box">
            <div class="mapChart" id="mapChart"></div>
        </div>
        <div class="right-box">
            <div class="top">
                <div class="title com-title">诉讼阶段分布</div>
                <div class="flex-box">
                    <div class="left">
                        <div class="item ga">
                            <div class="title">公安</div>
                            <div class="num">1,800<span>人</span></div>
                        </div>
                        <div class="line"></div>
                        <div class="item jcy">
                            <div class="title">检察院</div>
                            <div class="num">124<span>人</span></div>
                        </div>
                    </div>
                    <img class="mid-img" src="~@/assets/images/leaderScreen/rtb.png" alt="">
                    <div class="right">
                        <div class="item fy">
                            <div class="title">法院</div>
                            <div class="num">1,800<span>人</span></div>
                        </div>
                        <div class="line"></div>
                        <div class="item qt">
                            <div class="title">其他</div>
                            <div class="num">124<span>人</span></div>
                        </div>
                    </div>
                </div>
                <div class="charts">
                    <div id="myChartBoxD" class="myChartBoxD" ref="ChartBoxD"></div>
                </div>

            </div>
            <div class="mid">
                <div class="title com-title">案件分布TOP5</div>
                <div class="charts">
                    <div id="myChartBoxE" class="myChartBoxE" ref="ChartBoxE"></div>
                </div>

            </div>
            <div class="btm">
                <div class="title com-title">新收人员</div>
                <div class="newly-box">
                    <div class="newly">
                        <div class="day">
                            <img class="day-img com-img" src="~@/assets/images/leaderScreen/jrsy.png" alt="">
                            <span class="day-sy com-sy">今日收押</span>
                            <span class="day-num com-num">8</span>
                            <span class="day-ye">较昨日<span class="add">+5</span></span>
                        </div>
                        <div class="year">
                            <img src="~@/assets/images/leaderScreen/jnljsy.png" alt="" class="year-img com-img">
                            <span class="day-sy  com-sy">今年累计收押</span>
                            <span class="day-num com-num">8</span>
                        </div>
                    </div>
                    <div class="subtitle">近一年新收入所人员趋势</div>
                    <div class="charts">
                        <div id="myChartBoxC" class="myChartBoxC" ref="ChartBoxC"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import * as echarts from "echarts";
import bazxzs1Img from '@/assets/images/leaderScreen/bazxzs1.png';
import bazxzs2Img from '@/assets/images/leaderScreen/bazxzs2.png';
import beijingData from './beijing.json';
import { f } from "vue-slick-carousel";
export default {
    name: "generalTeam",
    data() {
        return {
            optionA: {},
            myChartA: null,
            optionB: {},
            myChartB: null,
            optionD: {},
            myChartD: null,
            optionE: {},
            myChartE: null,
            optionMap: {},
            myChartMap: null,
        };
    },
    mounted() {
        this.makeChartA()
        this.makeChartB()
        this.makeChartD()
        this.makeChartE()
        this.makeChartC()
        this.makeMapChart()
    },
    methods: {
        makeChartA() {
            this.myChartA = echarts.init(document.getElementById("myChartBoxA"));
            this.optionA = {
                xAxis: {
                    axisLine: {
                        show: false,
                    }, // 隐藏轴线
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#D6F6FF',
                            fontsize: '14px',

                        },
                        interval: 0, // 显示所有标签
                        width: 40, // 设置宽度
                        overflow: 'break' // 超出宽度自动换行
                    },
                    type: 'category',
                    data: ['北京市拘留所', '第二看守所', '第三看守所', '第三看守所', '第六看守所', '强制戒毒所', '强制医疗所'],
                },
                grid: {
                    left: '0',
                    right: '5%',
                    bottom: '8%',
                    top: '12%',
                    containLabel: true // 确保包含坐标轴标签
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    },
                    backgroundColor: '#00334199',  // 背景色
                    extraCssText: 'backdrop-filter: blur(8px);',// 背景模糊
                    borderColor: '#003341',      // 边框色
                    textStyle: {              // 文字样式
                        color: '#CDFBFE',
                    },
                    formatter: function (params) { // params[0].data.nameCode
                        return (
                            params[0].name +
                            "<br>" +
                            "在押人数：" +
                            params[0].value
                        );
                    }
                },
                yAxis: [{
                    type: 'value',

                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#8CAFBF',
                            fontsize: '14px'
                        }
                    },
                    axisLine: { show: false }, // 隐藏轴线
                    splitLine: {
                        lineStyle: {
                            color: '#8CAFBF',
                            type: 'dashed'
                        }

                    }
                }],
                series: [
                    {
                        name: '在押人数',
                        data: [120, 100, 150, 80, 70, 20, 10],
                        type: 'bar',
                        barWidth: 8,
                        // barWidth: '20%',
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#00BAFF'
                                }, {
                                    offset: 1,
                                    color: '#0D556B'
                                }]),
                                opacity: 1
                            }
                        },
                        yAxisIndex: 0
                    },
                    {
                        name: 'hill',
                        type: 'bar',
                        // barWidth: 16,
                        barWidth: '40%',
                        barGap: '-175%',
                        data: [150, 150, 150, 150, 150, 150, 150],
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 1,
                                color: '#006DFF'
                            }, {
                                offset: 0,
                                color: '#00EAFF'
                            }]),
                            opacity: 0.08
                        },
                        zlevel: 9
                    },]

            }
            this.myChartA.setOption(this.optionA);
        },
        makeChartB() {
            this.myChartB = echarts.init(document.getElementById("myChartBoxB"));
            this.optionB = {
                xAxis: {
                    axisLine: {
                        show: false,
                    }, // 隐藏轴线
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#D6F6FF',
                            fontsize: '14px',

                        },
                        interval: 0, // 显示所有标签
                        width: 40, // 设置宽度
                        overflow: 'break' // 超出宽度自动换行
                    },
                    type: 'category',
                    data: ['北京市拘留所', '第二看守所', '第三看守所', '第三看守所', '第六看守所', '强制戒毒所', '强制医疗所'],
                },
                grid: {
                    left: '0',
                    right: '5%',
                    bottom: '2%',
                    top: '12%',
                    containLabel: true // 确保包含坐标轴标签
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    },
                    backgroundColor: '#00334199',  // 背景色
                    extraCssText: 'backdrop-filter: blur(8px);',// 背景模糊
                    borderColor: '#003341',      // 边框色
                    textStyle: {              // 文字样式
                        color: '#CDFBFE',
                    },
                    formatter: function (params) { // params[0].data.nameCode
                        return (
                            params[0].name +
                            "<br>" +
                            "警押比：" +
                            params[0].value
                        );
                    }
                },
                yAxis: [{
                    type: 'value',

                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#8CAFBF',
                            fontsize: '14px'
                        }
                    },
                    axisLine: { show: false }, // 隐藏轴线
                    splitLine: {
                        lineStyle: {
                            color: '#8CAFBF',
                            type: 'dashed'
                        }

                    }
                }],
                series: [
                    {
                        name: '在押人数',
                        data: [2.0, 2.5, 1, 0.5, 1, 2.3, 1.4],
                        type: 'bar',
                        barWidth: 8,
                        // barWidth: '20%',
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#FCDC92'
                                }, {
                                    offset: 1,
                                    color: '#497E7A'
                                }]),
                                opacity: 1
                            }
                        },
                        yAxisIndex: 0
                    },
                    {
                        name: 'hill',
                        type: 'bar',
                        // barWidth: 16,
                        barWidth: '40%',
                        barGap: '-175%',
                        data: [2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5],
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 1,
                                color: '#006DFF'
                            }, {
                                offset: 0,
                                color: '#00EAFF'
                            }]),
                            opacity: 0.08
                        },
                        zlevel: 9
                    },]

            }
            this.myChartB.setOption(this.optionB);
        },
        makeChartD() {
            this.myChartD = echarts.init(document.getElementById('myChartBoxD'));
            this.optionD = {
                graphic: [{
                    type: 'image',
                    left: 55,
                    top: 47,        // 距离顶部
                    style: {
                        image: bazxzs1Img,
                        width: 38,
                        height: 40
                    },
                    z: 8
                },
                {
                    type: 'image',
                    left: 55,
                    top: 44,        // 距离顶部
                    style: {
                        image: bazxzs2Img,
                        width: 38,
                        height: 40
                    },
                    z: 5
                },
                {
                    type: 'text',
                    left: 46,
                    top: 95,
                    style: {
                        text: '诉讼阶段分布',
                        fontSize: 10,
                        fill: '#D6F6FF',
                    },
                    z: 5
                }
                ],
                tooltip: {
                    show: true,
                    trigger: 'item'
                },
                legend: {
                    icon: 'circle',
                    itemWidth: 8,
                    itemHeight: 8,
                    itemGap: 20,
                    orient: 'vertical',  // 垂直排列图例
                    right: '5%',         // 距离右侧5%
                    top: 'center',
                    textStyle: {
                        fontSize: 12,
                        color: '#8CAFBF'
                    }
                },
                series: [
                    {
                        type: 'pie',
                        radius: ['70%', '85%'],
                        center: ['18%', '50%'],
                        // hoverAnimation: false,
                        z: 10,
                        padAngle: 3,
                        data: [
                            {
                                value: 30,
                                name: '刑事拘留',
                                itemStyle: {
                                    color: '#67D470',
                                }
                            },
                            {
                                value: 25,
                                name: '逮捕',
                                itemStyle: {
                                    color: '#BCBF5C',
                                },
                            },
                            {
                                value: 25,
                                name: '公安侦查',
                                itemStyle: {
                                    color: '#0DAEE3',
                                },
                            },
                            {
                                value: 25,
                                name: '审查起诉',
                                itemStyle: {
                                    color: '#3182EA',
                                },
                            },
                            {
                                value: 25,
                                name: '一审',
                                itemStyle: {
                                    color: '#5A70BC',
                                },
                            },
                            {
                                value: 25,
                                name: '二审',
                                itemStyle: {
                                    color: '#32498E',
                                },
                            },
                            {
                                value: 25,
                                name: '发回重审',
                                itemStyle: {
                                    color: '#549BF1',
                                },
                            },
                            {
                                value: 25,
                                name: '死刑复核',
                                itemStyle: {
                                    color: '#8B8BF6',
                                },
                            },
                            {
                                value: 25,
                                name: '补充侦查',
                                itemStyle: {
                                    color: '#AD8B6E',
                                },
                            },
                            {
                                value: 25,
                                name: '待执行',
                                itemStyle: {
                                    color: '#3258DB',
                                },
                            },
                            {
                                value: 25,
                                name: '已决',
                                itemStyle: {
                                    color: '#729326',
                                },
                            },
                            {
                                value: 25,
                                name: '其他',
                                itemStyle: {
                                    color: '#22C8DA',
                                },
                            },
                        ],
                        labelLine: {
                            show: false,
                        },
                        label: {
                            show: false  // 不显示标签
                        },
                    }
                ],
            }
            this.myChartD.setOption(this.optionD);
        },
        makeChartE() {
            this.myChartE = echarts.init(document.getElementById('myChartBoxE'));
            this.optionE = {
                tooltip: {
                    trigger: "axis",
                    backgroundColor: '#00334199',  // 背景色
                    borderColor: '#003341',      // 边框色
                    extraCssText: 'backdrop-filter: blur(8px);',// 背景模糊
                    textStyle: {              // 文字样式
                        color: '#CDFBFE',
                    },
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                    },
                    // borderRadius: 5,
                    // borderColor: "#6baab2",
                    // borderWidth: 1,
                },
                grid: {
                    left: "4%",
                    right: "4%",
                    bottom: "5%",
                    top: "10%",
                    containLabel: true
                },
                xAxis: {
                    data: ['诈骗案', '盗窃案', '贩卖毒品案', '帮信罪', '隐瞒犯罪所得罪'],
                    // triggerEvent: true,
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: '#D6F6FF'
                        }
                    },

                },
                yAxis: {
                    splitLine: {
                        // show: true,
                        lineStyle: {
                            color: '#8CAFBF50',
                            type: 'dashed'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {

                        lineStyle: {
                            color: '#8CAFBF'
                        }

                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            fontSize: 14
                        }
                    }
                },
                // color: ["#e54035"],
                series: [{
                    name: "数量",
                    barMinHeight: 10,
                    type: "pictorialBar",
                    barCategoryGap: "30%",
                    // symbol: 'path://M0,10 L10,10 L5,0 L0,10 z',
                    symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
                    itemStyle: {
                        normal: {
                            //barBorderRadius: 5,
                            //渐变色
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 1,
                                color: "#09D4F690"
                            },
                            {
                                offset: 0.5,
                                color: "#09D4F690"
                            },
                            {
                                offset: 0,
                                color: "#F4C345"
                            }
                            ])
                        }
                    },
                    data: [70, 60, 80, 90, 30],
                    z: 10
                },
                ]
            }
            this.myChartE.setOption(this.optionE);
        },
        makeChartC() {
            this.myChartC = echarts.init(document.getElementById("myChartBoxC"));
            this.optionC = {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: '#00334199',  // 背景色
                    borderColor: '#003341',      // 边框色
                    extraCssText: 'backdrop-filter: blur(8px);',// 背景模糊
                    textStyle: {              // 文字样式
                        color: '#CDFBFE',
                    },
                    axisPointer: {
                        lineStyle: {
                            color: '#57617B'
                        }
                    }
                },

                grid: {
                    left: '0',
                    right: '5%',
                    bottom: '2%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: '#D6F6FF'
                        }

                    }, // 隐藏轴线
                    axisTick: {
                        show: false
                    },

                    // boundaryGap: [0, '20%'],
                    boundaryGap: false,
                    data: ['2024/9', '2024/10', '2024/11', '2024/12', '2025/1']
                }],
                yAxis: [{
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#8CAFBF'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#8CAFBF50',
                            type: 'dashed'
                        }

                    },
                    // axisLine: { show: false }, // 隐藏轴线
                    axisLabel: {
                        textStyle: {
                            fontSize: 14
                        }
                    },

                }],
                series: [{
                    name: '数量',
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 6,
                    showSymbol: false,
                    lineStyle: {
                        normal: {
                            width: 2
                        }
                    },
                    areaStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(73, 200, 255, 0.3)'
                            }, {
                                offset: 0.8,
                                color: 'rgba(73, 200, 255, 0)'
                            }], false),
                            shadowColor: 'rgba(0, 0, 0, 0.1)',
                            shadowBlur: 10
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#49C8FF',
                            // borderColor: 'rgba(0,136,212,0.2)',
                            // borderWidth: 12

                        }
                    },
                    data: [10, 110, 125, 30, 122]
                }]
            }
            this.myChartC.setOption(this.optionC);
        },
        async makeMapChart() {
            // 地图代码
            this.myChartMap = echarts.init(document.getElementById('mapChart'));
            echarts.registerMap('beijing', beijingData)
            this.optionMap = {
                tooltip: {
                    trigger: 'item'
                },
                geo: [{
                    map: 'beijing',
                    layoutCenter: ['50%', '50.3%'],
                    layoutSize: '80%',
                    aspectScale: 0.8, //长宽比
                    // roam: 'scale', // 只允许缩放
                    zoom: 1.2,
                    roam: false,
                    label: {
                        normal: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            }
                        },
                        emphasis: {
                            textStyle: {
                                color: '#fff'
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            borderColor: 'rgba(30, 169, 211, 1)',
                            borderWidth: 3,
                            areaColor: 'rgba(0, 15, 40, 0.5)',
                            shadowColor: 'rgba(30, 169, 211, 1)',
                            shadowOffsetX: 0,
                            shadowOffsetY: 10,
                            shadowBlur: 10
                        },
                        emphasis: {
                            areaColor: '#389BB730',
                            color: 'green',
                            borderWidth: 1
                        }
                    }
                }],
                series: [
                    {
                        type: 'map',
                        roam: false,
                        layoutCenter: ['50%', '50%'],
                        layoutSize: '80%',
                        aspectScale: 0.8, //长宽比
                        label: {
                            normal: {
                                show: true,
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                            emphasis: {
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: '#2ab8ff',
                                borderWidth: 0.5,
                                areaColor: 'rgba(7, 16, 48, 1)',
                                shadowColor: 'rgba(30, 169, 211, 1)',
                                shadowBlur: 0,
                                shadowOffsetX: 0,
                                shadowOffsetY: 1,
                            },
                            emphasis: {
                                areaColor: 'rgba(5, 45, 94, 1)',
                                borderWidth: 1,
                                shadowBlur: 5,
                                shadowOffsetX: 0,
                                shadowOffsetY: 1,
                            },
                        },
                        zoom: 1.2,
                        map: 'beijing',
                    },
                ]
            }
            this.myChartMap.setOption(this.optionMap)

        },
    },
};

</script>

<style scoped lang="less">
@import "./index.less";
</style>