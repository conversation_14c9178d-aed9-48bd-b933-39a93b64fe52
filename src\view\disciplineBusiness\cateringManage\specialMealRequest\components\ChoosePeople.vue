<template>
  <!-- 被监管人员选择组件 -->
  <div>
    <div class="personnel-selector">
      <!-- 未选择人员时的选择区域-->
      <div class="personnel-select-area" @click="openPrison">
        <div class="personnel-select-icon">
          <Icon type="ios-people" />
        </div>
        <div class="personnel-select-text">点击选择被监管人员</div>
      </div>
    </div>
    <!-- 被监管人员选择组件 -->
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
      title="人员列表">
      <div class="select-use">
        <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS" :isMultiple="true" :selectUseIds="selectedIds" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="useSelect" class="save">确 定</Button>
        <Button @click="openModal = false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { prisonSelect } from "sd-prison-select";

export default {
  components: {
    prisonSelect,
  },
  props: {
    selectedIds: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      openModal: false,
      selectList: [],
    };
  },
  methods: {
    openPrison() {
      this.openModal = true;
    },
    useSelect() {
      if (
        this.$refs.prisonSelect.checkedUse &&
        this.$refs.prisonSelect.checkedUse.length > 0
      ) {
        this.selectList = this.$refs.prisonSelect.checkedUse.map(item => {
          return {
            jgrybm: item.jgrybm,
            jgryxm: item.xm,
            id: item.id,
            roomName: item.roomName,
            roomId: item.jsh,
          }
        });
        this.$emit("selectUser", this.selectList);
        this.openModal = false;
      } else {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择人员!",
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 选择区域样式
.personnel-select-area {
  border: 2px dashed #bfbfbf;
  height: 160px;
  border-radius: 4px;
  background: #fafafa;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1c4e80;
    background: #f0f5ff;

    .personnel-select-icon {
      background: #1c4e80;

      .ivu-icon {
        color: #fff;
      }
    }

    .personnel-select-text {
      color: #1c4e80;
    }
  }

  .personnel-select-icon {
    width: 56px;
    height: 56px;
    background: #e8f4fd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    transition: all 0.3s ease;

    .ivu-icon {
      font-size: 24px;
      color: #1c4e80;
      transition: color 0.3s ease;
    }
  }

  .personnel-select-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
  }
}
</style>
