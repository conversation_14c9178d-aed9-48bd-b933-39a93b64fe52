<template>
  <div>
    <div class="table-container">
      <s-DataGrid ref="grid" funcMark="jspccptz" :customFunc="true" :params="params">
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="primary" v-if="func.includes(globalAppCode + ':jspccptz:detail')" @click.native="handleDetail(index, row)">
            详情
          </Button>&nbsp;
          <Button type="primary" v-if="func.includes(globalAppCode + ':jspccptz:export')" @click.native="handleExport(index, row)">
            导出
          </Button>
        </template>
      </s-DataGrid>
    </div>

    <!-- 详情弹窗 -->
    <Modal
      v-model="showDetailModal"
      :title="modalTitle"
      width="90%"
      :mask-closable="false"
      :closable="!autoExport"
      class-name="recipe-detail-modal">
      <detail
        v-if="showDetailModal"
        @close="handleDetailClose"
        :cookId="cookId"
        :autoExport="autoExport" />
      <div slot="footer" v-if="!autoExport">
        <Button @click="handleDetailClose">关闭</Button>
      </div>
    </Modal>

    <!-- 导出弹窗 -->
    <Modal
      v-model="showExportModal"
      :title="exportModalTitle"
      width="90%"
      :mask-closable="false"
      :closable="false"
      class-name="recipe-export-modal">
      <detail
        v-if="showExportModal"
        @close="handleExportClose"
        :cookId="exportCookId"
        :autoExport="true" />
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import {sDataGrid} from 'sd-data-grid'
import {mapActions} from 'vuex'
import detail from "./detail.vue"
import reportReview from "../../infoManage/informationManage/reportReview.vue"
import exportMixin from '@/mixins/exportMixin'

export default {
  name: "recipeLedger",
  mixins: [exportMixin],
  components: {
    reportReview,
    sDataGrid,
    detail
  },
  data() {
    return {
      params: {},
      // 详情弹窗相关
      showDetailModal: false,
      cookId: '',
      // 导出弹窗相关
      showExportModal: false,
      exportCookId: ''
    }

  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleAddRecipeTem() {
    },
    // 查看详情
    handleDetail(index, row) {
      this.showDetailModal = true
      this.cookId = row.id
    },

    // 导出功能 - 弹窗方式自动导出
    handleExport(index, row) {
      this.showExportModal = true
      this.exportCookId = row.id
    },

    // 详情弹窗关闭处理
    handleDetailClose() {
      this.showDetailModal = false
      this.cookId = ''
    },

    // 导出弹窗关闭处理
    handleExportClose(exported = false) {
      this.showExportModal = false
      this.exportCookId = ''

      // 如果是导出操作完成，显示成功消息
      if (exported) {
        this.$Message.success('导出成功！')
      }
    },

    handleDelete() {

    }
  },

  created() {
  },

  computed: {
    // 弹窗标题
    modalTitle() {
      return '菜谱台账详情'
    },

    // 导出弹窗标题
    exportModalTitle() {
      return '正在导出菜谱...'
    }
  },

}

</script>

<style scoped lang="less">
.table-container {
  margin: 10px 15px;
}

// 弹窗样式优化
.recipe-detail-modal, .recipe-export-modal {
  .ivu-modal-body {
    padding: 0;
    max-height: 80vh;
    overflow-y: auto;
  }

  .ivu-modal-header {
    border-bottom: 1px solid #e8eaec;
    padding: 16px 24px;
  }

  .ivu-modal-footer {
    border-top: 1px solid #e8eaec;
    padding: 12px 24px;
    text-align: center;
  }
}

// 导出弹窗特殊样式
.recipe-export-modal {
  .ivu-modal-header {
    background: linear-gradient(135deg, #f0f9ff, #e6f3ff);
    color: #2b5fda;
    font-weight: 600;
  }
}
</style>
