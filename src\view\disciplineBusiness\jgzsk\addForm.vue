<template>
  <div>
    <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="140" :label-colon="true" style="margin-top: 15px;">
        <Row>
            <Col span="12">
                <FormItem label="知识库名称" prop="knowledgeName" style="width: 100%;" :rules="[{ trigger: 'blur', message: '知识库名称为必填', required: true }]">
                    <Input v-model="formData.knowledgeName" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
            </Col>
            <Col span="12">
                <FormItem label="知识库类型" prop="knowledgeType" style="width: 100%;" :rules="[{ trigger: 'blur', message: '知识库类型为必填', required: true }]">
                    <s-dicgrid v-model="formData.knowledgeType" dicName="ZD_PM_KNOWLEDGE_TYPE" />
                </FormItem>
            </Col>  
            <Col span="24">
                <FormItem label="摘要" prop="summary" style="width: 100%;">
                    <Input v-model="formData.summary" type="textarea" :autosize="{minRows: 5,maxRows: 6}"></Input>
                </FormItem>
            </Col>
            <Col span="12">
                <FormItem label="上传附件" prop="attUrl" style="width: 100%;" :rules="[{ trigger: 'blur', message: '请填写', required: true }]">
                    <!-- <file-upload :key="formData.attUrl" :serviceMark="serviceMark"
                    :bucketName="bucketName" :defaultList="fileList" :beforeUpload="() => true"
                    @fileComplete="fileCompleteFile" /> -->
                    <FileUpload
                        :mode="mode"
                        :default-list="fileList"
                        :width="100"
                        :height="100"
                        :auto-upload="true"
                        @on-success="handleSuccess"
                        @on-remove="handleRemove"
                        :format="['jpg', 'png', 'pdf','xls','xlsx','mp4']"
                        style="margin-top: 10px;"
                    />
                </FormItem>
            </Col>
        </Row>
    </Form>

    <div class='bsp-base-fotter' style="text-algin: center;">
        <Button @click="toback">取消</Button>
        <Button type="primary" @click="submitJb" :loading="loadingsignIn">确认</Button>
    </div>
  </div>
</template>

<script>
import FileUpload from '@/components/bsp-upload/FileUpload.vue'
export default {  
    components: {
        FileUpload
    },
    data(){
        return {
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            formData: {
                attUrl: ''
            },
            fileList: [],
            ruleValidate: {},
            loadingsignIn: false,
            mode: 'edit'
        }
    },
    props: {
        curId:String,
        saveType:String
    },
    watch: {
        curId:{
            immediate: true,
            handler(value) {
                if(value) {
                    console.log(value,'curId');
                    if(this.saveType !== 'add') {
                        this.getData(value)
                    }
                }
            },
            deep: true
        }
    },
    methods: {
        fileCompleteFile(file) {
          this.fileList = file
          this.$set(this.formData,'attUrl',JSON.stringify(file))
          console.log(file,'file');
          let str = file.map(i => i.fileName).join(',')
          console.log(str);
          this.$set(this.formData,'knowledgeName',str)
        },
        // 附件上传成功回调
        handleSuccess(file, res, uploadList) {
            console.log(file, res, uploadList,'file, res, uploadList');
            this.fileList = uploadList
            this.$set(this.formData,'attUrl',JSON.stringify(uploadList))
            let str = uploadList.map(i => i.name).join(',')
            this.$set(this.formData,'knowledgeName',str)
        },
        // 附件删除回调
        handleRemove(file, fileList) {
            this.$set(this.formData,'attUrl',JSON.stringify(fileList))
            let str = fileList.map(i => i.name).join(',')
            this.$set(this.formData,'knowledgeName',str)
        },
        submitJb() {
          console.log(this.saveType,'saveType');
            
          console.log(this.formData,'this.formData');
          
          this.$refs.formData.validate(valid => {
            if(valid) {
              console.log(this.formData,'提交数据');
              this.loadingsignIn = true
              this.saveData()
            } else {
              this.$Message.error('请填写完整内容!')
            }
          })
        },
        saveData() {
          let url = ''
          if(this.saveType == 'add') {
            url = this.$path.app_knowledgeBase_create
          } else if(this.saveType == 'edit') {
            url = this.$path.app_knowledgeBase_update
          }

          this.$store.dispatch('authPostRequest',{
            url,
            params: this.formData
          }).then(res => {
            if(res.success) {
                this.$Message.success('新增成功!')
              this.loadingsignIn = false
              this.$nextTick(() => {
                this.toback()
              })
            } else {
              this.loadingsignIn = false
              this.$Message.error(res.msg || '接口操作失败!')
            }
          })
        },
        getData(id) {
            this.$store.dispatch('authGetRequest',{
                url: this.$path.app_knowledgeBase_get,
                params: {
                    id
                }
            }).then(res => {
                if(res.success) {
                    this.formData = res.data
                    this.fileList = res.data.attUrl ? JSON.parse(res.data.attUrl) : []
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        toback() {
            this.$emit('toback')
        }
    },
    created() {
        if(this.saveType && this.saveType == 'add'){
            console.log('11111111');
            
            this.formData = {}
            this.fileList = []
        }
    }
}
</script>

<style>

</style>