import layoutMenu from '@/components/layoutMenu/index.vue'


export default [
	{
		path: "/discipline",
		name: "discipline",
		meta: {
			title: "管教业务",
		},
		component: layoutMenu,
		children: [
			{
				path: "informationManage",
				name: "informationManage",
				meta: {
					title: "监室信息管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/roomManage/informationManage/index.vue"),
			},
			{
				path: "informationrelease",
				name: "informationrelease",
				meta: {
					title: "监室信息发布",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/roomManage/informationrelease/index.vue"),
			},
			{
				path: "keypersonnel",
				name: "keypersonnel",
				meta: {
					title: "重点人员关注",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/roomManage/keypersonnel/index.vue"),
			},
			{
				path: "bedManagement",
				name: "bedManagement",
				meta: {
					title: "床位管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/roomManage/bedManagement/index.vue"),
			},
			{
				path: "prisonRoomDuty",
				name: "prisonRoomDuty",
				meta: {
					title: "监室值班设置",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prisonRoomDuty/roomHome.vue"),
			},
			{
				path: "prisonRoomDutyRecord",
				name: "prisonRoomDutyRecord",
				meta: {
					title: "值班台账",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prisonRoomDuty/record.vue"),
			},
			{
				path: "dutyRulesConfig",
				name: "dutyRulesConfig",
				meta: {
					title: "值班规则配置",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prisonRoomDuty/dutyRulesConfig.vue"),
			},
			{
				path: "prisonRoomAffairs",
				name: "prisonRoomAffairs",
				meta: {
					title: "监室值日设置",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prisonRoomAffairs/roomHome.vue"),
			},
			{
				path: "prisonRoomAffairsRecord",
				name: "prisonRoomAffairsRecord",
				meta: {
					title: "值日台账",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prisonRoomAffairs/record.vue"),
			},
			{
				path: "prisonRoomAffairsConfig",
				name: "prisonRoomAffairsConfig",
				meta: {
					title: "值日播报配置",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prisonRoomAffairs/dutyRulesConfig.vue"),
			},
			{
				path: "informationManages",
				name: "informationManages",
				meta: {
					title: "信息报备管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/infoManage/informationManage/index.vue"),
			},
			{
				path: "infoSetting",
				name: "infoSetting",
				meta: {
					title: "报备事项管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/infoManage/infoSetting/index.vue"),
			},
			{
				path: "oneDaylifeSystem",
				name: "oneDaylifeSystem",
				meta: {
					title: "一日生活制度",
				},
				redirect: "/oneDaylifeSystem/oneDay",
				component: () => import("@/view/disciplineBusiness/oneDaylifeSystem/index.vue"),
				children: [
					{
						path: "oneDay",
						name: "oneDay",
						meta: {
							title: "一日生活制度",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/oneDaylifeSystem/oneDay/index.vue"),
					},
					{
						path: "templatemanagement",
						name: "templatemanagement",
						meta: {
							title: "模板管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/oneDaylifeSystem/templatemanagement/index.vue"),
					},
					{
						path: "transactionmanagement",
						name: "transactionmanagement",
						meta: {
							title: "事务管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/oneDaylifeSystem/transactionmanagement/index.vue"),
					},
					{
						path: "holidaysettings",
						name: "holidaysettings",
						meta: {
							title: "节假日设置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/oneDaylifeSystem/holidaysettings/index.vue"),
					},
				],
			},
			{
				path: "restraintsUsed",
				name: "restraintsUsed",
				meta: {
					title: "戒具使用",
				},
				redirect: "/discipline/restraintsUsed/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "restraintsUsedList",
						meta: {
							title: "戒具使用",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsed/index.vue"),
					},
					{
						path: "detail",
						name: "restraintsUsedDetail",
						meta: {
							title: "戒具使用详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsed/detail.vue"),
					},
					{
						path: "add",
						name: "restraintsUsedAdd",
						meta: {
							title: "戒具使用呈批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsed/add.vue"),
					},
					{
						path: "update",
						name: "restraintsUsedUpdate",
						meta: {
							title: "戒具使用修改",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsed/update.vue"),
					},
					{
						path: "check",
						name: "restraintsUsedCheck",
						meta: {
							title: "戒具使用审批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsed/check.vue"),
					},
				],
			},
			{
				path: "restraintsUsedCheck",
				name: "restraintsUsedCheck",
				meta: {
					title: "戒具使用审批",
				},
				redirect: "/discipline/restraintsUsedCheck/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "restraintsUsedCheckList",
						meta: {
							title: "戒具使用审批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsedCheck/index.vue"),
					},
					{
						path: "check",
						name: "restraintsUsedCheckUpdate",
						meta: {
							title: "戒具使用审批-审批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsedCheck/check.vue"),
					},
					{
						path: "detail",
						name: "restraintsUsedCheckDetail",
						meta: {
							title: "戒具使用详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsUsedCheck/detail.vue"),
					},
				],
			},
			{
				path: "restraintsCheck",
				name: "restraintsCheck",
				meta: {
					title: "戒具检查",
				},
				redirect: "/discipline/restraintsCheck/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "restraintsCheckList",
						meta: {
							title: "戒具检查台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsCheck/index.vue"),
					},
					{
						path: "create",
						name: "restraintsCheckCreate",
						meta: {
							title: "戒具检查登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsCheck/create.vue"),
					},
					{
						path: "detail",
						name: "restraintsCheckDetail",
						meta: {
							title: "戒具检查详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/restraintsCheck/detail.vue"),
					},
				],
			},
			{
				path: "appointmentReviewManagement",
				name: "appointmentReviewManagement",
				meta: {
					title: "预约审核管理",
				},
				redirect: "/discipline/appointmentReviewManagement/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "appointmentReviewManagementList",
						meta: {
							title: "预约审核管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/appointmentReviewManagement/index.vue"),
					},
					{
						path: "detail",
						name: "appointmentReviewManagementDetail",
						meta: {
							title: "预约审核详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/appointmentReviewManagement/detail.vue"),
					},
					{
						path: "settings",
						name: "appointmentReviewManagementSettings",
						meta: {
							title: "预约审核配置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/appointmentReviewManagement/settings.vue"),
					},
				],
			},
			{
				path: "riskRank-kss",
				name: "riskRank-kss",
				meta: {
					title: "风险评估",
				},
				redirect: "/discipline/riskRank-kss/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "riskRankList",
						meta: {
							title: "风险评估",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-kss/index.vue"),
					},
					{
						path: "create",
						name: "riskRankListCreate",
						meta: {
							title: "风险评估-创建风险评估",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-kss/create.vue"),
					},
					{
						path: "update",
						name: "riskRankListUpdate",
						meta: {
							title: "风险评估-评估登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-kss/update.vue"),
					},
					{
						path: "check",
						name: "riskRankListCheck",
						meta: {
							title: "风险评估-评估审核",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-kss/check.vue"),
					},
					{
						path: "detail",
						name: "riskRankDetail",
						meta: {
							title: "风险评估详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-kss/detail.vue"),
					},
				],
			},
			{
				path: "riskRank-jls",
				name: "riskRank-jls",
				meta: {
					title: "风险评估-拘留所",
				},
				redirect: "/discipline/riskRank-jls/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "riskRankJlsList",
						meta: {
							title: "风险评估",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-jls/index.vue"),
					},
					{
						path: "create",
						name: "riskRankListJlsCreate",
						meta: {
							title: "风险评估-创建风险评估",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-jls/create.vue"),
					},
					{
						path: "update",
						name: "riskRankListJlsUpdate",
						meta: {
							title: "风险评估-评估登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-jls/update.vue"),
					},
					{
						path: "check",
						name: "riskRankListJlsCheck",
						meta: {
							title: "风险评估-评估审核",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-jls/check.vue"),
					},
					{
						path: "detail",
						name: "riskRankJlsDetail",
						meta: {
							title: "风险评估详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/riskRank-jls/detail.vue"),
					},
				],
			},
			{
				path: "transJailRoom",
				name: "transJailRoom",
				meta: {
					title: "过渡管理",
				},
				redirect: "/discipline/transJailRoom/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "transJailRoomList",
						meta: {
							title: "过渡管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/transJailRoom/index.vue"),
					},
					{
						path: "create",
						name: "transJailRoomCreate",
						meta: {
							title: "过渡管理-延长过渡呈批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/transJailRoom/create.vue"),
					},
					{
						path: "check",
						name: "transJailRoomCheck",
						meta: {
							title: "过渡管理-延长过渡审批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/transJailRoom/create.vue"),
					},
					{
						path: "detail",
						name: "transJailRoomDetail",
						meta: {
							title: "过渡管理详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/transJailRoom/detail.vue"),
					},
				],
			},
			{
				path: "behaviorIdentification-kss",
				name: "behaviorIdentification-kss",
				meta: {
					title: "人员表现鉴定",
				},
				redirect: "/discipline/behaviorIdentification-kss/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "behaviorIdentificationKssList",
						meta: {
							title: "人员表现鉴定",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-kss/index.vue"),
					},
					{
						path: "create",
						name: "behaviorIdentificationKssCreate",
						meta: {
							title: "人员表现鉴定-登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-kss/create.vue"),
					},
					{
						path: "check",
						name: "behaviorIdentificationKssCheck",
						meta: {
							title: "人员表现鉴定-审批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-kss/check.vue"),
					},
					{
						path: "print",
						name: "behaviorIdentificationKssPrint",
						meta: {
							title: "人员表现鉴定-打印预览",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-kss/print.vue"),
					},
					{
						path: "detail",
						name: "behaviorIdentificationKssDetail",
						meta: {
							title: "人员表现鉴定详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-kss/detail.vue"),
					},
				],
			},
			{
				path: "behaviorIdentification-jls",
				name: "behaviorIdentification-jls",
				meta: {
					title: "人员表现鉴定",
				},
				redirect: "/discipline/behaviorIdentification-jls/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "behaviorIdentificationJlsList",
						meta: {
							title: "人员表现鉴定",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-jls/index.vue"),
					},
					{
						path: "create",
						name: "behaviorIdentificationJlsCreate",
						meta: {
							title: "人员表现鉴定-登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-jls/create.vue"),
					},
					{
						path: "check",
						name: "behaviorIdentificationJlsCheck",
						meta: {
							title: "人员表现鉴定-审批",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-jls/check.vue"),
					},
					{
						path: "print",
						name: "behaviorIdentificationJlsPrint",
						meta: {
							title: "人员表现鉴定-打印预览",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-jls/print.vue"),
					},
					{
						path: "detail",
						name: "behaviorIdentificationJlsDetail",
						meta: {
							title: "人员表现鉴定详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/behaviorIdentification-jls/detail.vue"),
					},
				],
			},
			{
				path: "noticeOfRightsAndObligations-kss",
				name: "noticeOfRightsAndObligations-kss",
				meta: {
					title: "权利义务告知",
				},
				redirect: "/discipline/noticeOfRightsAndObligations-kss/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "noticeOfRightsAndObligationsKssList",
						meta: {
							title: "权利义务告知",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/noticeOfRightsAndObligations-kss/index.vue"),
					},
					{
						path: "detail",
						name: "noticeOfRightsAndObligationsKssDetail",
						meta: {
							title: "权利义务告知详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/noticeOfRightsAndObligations-kss/detail.vue"),
					},
				],
			},
			{
				path: "noticeOfRightsAndObligations-jls",
				name: "noticeOfRightsAndObligations-jls",
				meta: {
					title: "权利义务告知",
				},
				redirect: "/discipline/noticeOfRightsAndObligations-jls/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "noticeOfRightsAndObligationsJlsList",
						meta: {
							title: "权利义务告知",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/noticeOfRightsAndObligations-jls/index.vue"),
					},
					{
						path: "detail",
						name: "noticeOfRightsAndObligationsJlsDetail",
						meta: {
							title: "权利义务告知详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/noticeOfRightsAndObligations-jls/detail.vue"),
					},
				],
			},
			{
				path: "assistInSolvingCases",
				name: "assistInSolvingCases",
				meta: {
					title: "协助破案",
				},
				redirect: "/discipline/assistInSolvingCases/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "assistInSolvingCasesList",
						meta: {
							title: "协助破案",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/assistInSolvingCases/index.vue"),
					},
					{
						path: "create",
						name: "assistInSolvingCasesCreate",
						meta: {
							title: "协助破案登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/assistInSolvingCases/create.vue"),
					},
					{
						path: "detail",
						name: "assistInSolvingCasesDetail",
						meta: {
							title: "协助破案详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/assistInSolvingCases/detail.vue"),
					},
					{
						path: "check",
						name: "assistInSolvingCasesCheck",
						meta: {
							title: "协助破案登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/assistInSolvingCases/check.vue"),
					},
				],
			},
			// 此路由应该属于警戒看守目录下，但因为没有相关路由文件，因此不得不写在这里
			{
				path: "institutionSituation",
				name: "institutionSituation",
				meta: {
					title: "所情处置",
				},
				redirect: "/discipline/institutionSituation/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "institutionSituationList",
						meta: {
							title: "所情处置台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/institutionSituation/list/index.vue"),
					},
					{
						path: "detail",
						name: "institutionSituationDetail",
						meta: {
							title: "所情处置详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/institutionSituation/list/detail.vue"),
					},
					{
						path: "inspectionOrCheck",
						name: "institutionSituationAction",
						meta: {
							title: "所情处置操作",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/institutionSituation/list/inspectionOrCheck.vue"),
					},
					{
						path: "settle",
						name: "institutionSituationSettle",
						meta: {
							title: "所情处置流程",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/institutionSituation/list/settle.vue"),
					},
					{
						path: "manage",
						name: "institutionSituationManage",
						meta: {
							title: "所情类型配置管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/institutionSituation/manage/index.vue"),
					},
					{
						path: "settings",
						name: "institutionSituationSettings",
						meta: {
							title: "报警联动设置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/institutionSituation/settings/index.vue"),
					},
				],
			},
			// 此路由应该属于警戒看守目录下，但因为没有相关路由文件，因此不得不写在这里
			{
				path: "openJailRoomAtNight",
				name: "openJailRoomAtNight",
				meta: {
					title: "夜间开启监室",
				},
				redirect: "/discipline/openJailRoomAtNight/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "openJailRoomAtNightList",
						meta: {
							title: "夜间开启监室-台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/openJailRoomAtNight/index.vue"),
					},
					{
						path: "detail",
						name: "openJailRoomAtNightDetail",
						meta: {
							title: "夜间开启监室-详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/openJailRoomAtNight/detail.vue"),
					},
					{
						path: "create",
						name: "openJailRoomAtNightCreate",
						meta: {
							title: "夜间开启监室-创建",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/openJailRoomAtNight/create.vue"),
					},
					{
						path: "update",
						name: "openJailRoomAtNightUpdate",
						meta: {
							title: "夜间开启监室-操作",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/openJailRoomAtNight/update.vue"),
					}
				],
			},
			// 此路由应该属于基础功能目录下，但因为没有相关路由文件，因此不得不写在这里
			{
				path: "biometricComparison",
				name: "biometricComparison",
				meta: {
					title: "生物特征比对",
				},
				redirect: "/discipline/biometricComparison/index",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "index",
						name: "biometricComparisonList",
						meta: {
							title: "生物特征比对-台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/biometricComparison/index.vue"),
					},
					{
						path: "detail",
						name: "biometricComparisonDetail",
						meta: {
							title: "生物特征比对-详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/biometricComparison/detail.vue"),
					},
					{
						path: "create",
						name: "biometricComparisonCreate",
						meta: {
							title: "生物特征比对-登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/biometricComparison/create.vue"),
					},
					{
						path: "update",
						name: "biometricComparisonUpdate",
						meta: {
							title: "生物特征比对-反馈登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/biometricComparison/update.vue"),
					}
				],
			},
			{
				path: "catering",
				name: "catering",
				meta: {
					title: "监室配餐管理",
				},
				redirect: "/catering/dishesManage",
				component: () => import("@/view/disciplineBusiness/cateringManage/index.vue"),
				children: [
					{
						path: "dishesManage",
						name: "dishesManage",
						meta: {
							title: "菜品管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/dishesManage/index.vue"),
					},
					{
						path: "recipeTemplates",
						name: "recipeTemplates",
						meta: {
							title: "菜谱模板管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/recipeTemplates/index.vue"),
					},
					{
						path: "recipeConfigurationWeek",
						name: "recipeConfigurationWeek",
						meta: {
							title: "一周菜谱配置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/recipeConfigurationWeek/index.vue"),
					},
					{
						path: "recipeLedger",
						name: "recipeLedger",
						meta: {
							title: "菜谱台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/recipeLedger/index.vue"),
					},
					{
						path: 'recipeLedgerDetail',
						name: "recipeLedgerDetail",
						meta: {
							title: "菜谱详情",
							menu: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/recipeLedger/detail.vue"),
					},
					{
						path: "foodLedger",
						name: "foodLedger",
						meta: {
							title: "伙食台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/foodLedger/index.vue"),
					},
					{
						path: "specialMealRequest",
						name: "specialMealRequest",
						meta: {
							title: "特殊餐",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/specialMealRequest/index.vue"),
					},
					{
						path: "specialMealAudit",
						name: "specialMealAudit",
						meta: {
							title: "特殊餐申请审核",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/specialMealRequest/audit.vue"),
					},
					{
						path: "specialMealDetail",
						name: "specialMealDetail",
						meta: {
							title: "特殊餐申请详情",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/specialMealRequest/detail.vue"),
					},
					{
						path: "cookDelivery",
						name: "cookDelivery",
						meta: {
							title: "发饭确认",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/cateringManage/cookDelivery/index.vue"),
					}
				],
			},
			{
				path: "psy",
				name: "psy",
				meta: {
					title: "心理测评",
				},
				redirect: "/psy/table",
				component: () => import("@/view/disciplineBusiness/psy/index.vue"),
				children: [
					{
						path: "table",
						name: "table",
						meta: {
							title: "测评量表管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/psy/table.vue"),
					},
					{
						path: "plan",
						name: "plan",
						meta: {
							title: "测评计划管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/psy/plan.vue"),
					},
					{
						path: "tz",
						name: "tz",
						meta: {
							title: "测评台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/psy/tz.vue"),
					},
					{
						path: "previewPage",
						name: "previewPage",
						meta: {
							title: "题目预览",
							menu: false,
							bread: false,
						},
						component: () => import("@/view/disciplineBusiness/psy/previewPage.vue"),
					}
				]
			},
			{
				path: "/votingSurvey",
				name: "votingSurvey",
				meta: {
					title: "投票调查",
				},
				redirect: "/votingSurvey/table",
				component: () => import("@/view/disciplineBusiness/psy/index.vue"),
				children: [
					{
						path: "table",
						name: "table",
						meta: {
							title: "投票调查量表",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/psy/table.vue"),
					},
					{
						path: "plan",
						name: "plan",
						meta: {
							title: "投票调查计划",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/psy/plan.vue"),
					},
					{
						path: "tz",
						name: "tz",
						meta: {
							title: "投票调查台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/psy/tz.vue"),
					}
				]
			},
			{
				path: "securitycheck",
				name: "securitycheck",
				meta: {
					title: "安全检查管理",
				},
				redirect: "/securitycheck/dailycleaning",
				component: () => import("@/view/disciplineBusiness/securitycheck/index.vue"),
				children: [
					{
						path: "dailycleaning",
						name: "dailycleaning",
						meta: {
							title: "日常清监",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/securitycheck/dailycleaning/index.vue"),
					},
					{
						path: "detail",
						name: "detail",
						meta: {
							title: "详情",
							menu: false,
							bread: false,
						},
						component: () => import("@/view/disciplineBusiness/securitycheck/detail.vue"),
					},
					{
						path: "safetychecks",
						name: "safetychecks",
						meta: {
							title: "安全大检查",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/securitycheck/safetychecks/index.vue"),
					},
					{
						path: "checkconfiguration",
						name: "checkconfiguration",
						meta: {
							title: "检查项配置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/securitycheck/checkconfiguration/index.vue"),
					},
				],
			},
			{
				path: "InformationOfficer",
				name: "InformationOfficer",
				meta: {
					title: "信息员管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/InformationOfficer/index.vue"),
			},
			{
				path: "adjustmentRoom",
				name: "adjustmentRoom",
				meta: {
					title: "监室调整",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/adjustmentRoom/dataLedger.vue"),
			},
			{
				path: "registration",
				name: "registration",
				meta: {
					title: "出入登记",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/registration/index.vue"),
			},
			{
				path: "punishmentManagement",
				name: "punishmentManagement",
				meta: {
					title: "严管管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/punishmentManagement/index.vue"),
			},
			{
				path: "sanctionMentManagement",
				name: "sanctionMentManagement",
				meta: {
					title: "惩罚管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/sanctionMentManagement/index.vue"),
			},
			{
				path: "rewardManagement",
				name: "rewardManagement",
				meta: {
					title: "奖励管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/rewardManagement/index.vue"),
			},
			{
				path: "dwkf",
				name: "dwkf",
				meta: {
					title: "对外开放",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/dwkf/index.vue"),
			},
			{
				path: "flbz",
				name: "flbz",
				meta: {
					title: "法律帮助",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/flbz/index.vue"),
			},

			{
				path: "detentionUniform",
				name: "detentionUniform",
				meta: {
					title: "识别服管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/detentionUniform/index.vue"),
			},
			{
				path: "registration",
				name: "registration",
				meta: {
					title: "出入登记",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/registration/index.vue"),
			},
			{
				path: "confinement",
				name: "confinement",
				meta: {
					title: "禁闭",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/confinement/index.vue"),
			},
			{
				path: "AloneImprison",
				name: "AloneImprison",
				meta: {
					title: "单独关押",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/aloneImprison/index.vue"),
			},
			{
				path: "faceTofaceManage",
				name: "faceTofaceManage",
				meta: {
					title: "面对面管理",
				},
				redirect: "/faceTofaceManage/facetoface",
				component: () => import("@/components/empty-router-view/index.vue"),
				children: [
					{
						path: "facetoface",
						name: "facetoface",
						meta: {
							title: "面对面管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/faceTofaceManage/facetoface.vue"),
					},
					{
						path: "checkManage",
						name: "checkManage",
						meta: {
							title: "面对面管理配置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/faceTofaceManage/checkManage.vue"),
					},
				]
			},
			{
				path: "prohibitedManage",
				name: "prohibitedManage",
				meta: {
					title: "违禁品管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/prohibitedManage/index.vue"),
			},
			{
				path: "rollCall",
				name: "rollCall",
				meta: {
					title: "监室点名",
				},
				redirect: "/rollCall/jailRollCall",
				component: () => import("@/view/disciplineBusiness/rollCall/index.vue"),
				children: [
					{
						path: "jailRollCall",
						name: "jailRollCall",
						meta: {
							title: "监室点名",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/rollCall/jailRollCall/index.vue"),
					},
					{
						path: "autoRollCall",
						name: "autoRollCall",
						meta: {
							title: "自动点名设置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/rollCall/autoRollCall/index.vue"),
					},
				]
			},
			{
				path: "violation",
				name: "violation",
				meta: {
					title: "违规登记",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/violation/index.vue"),
			},
			{
				path: "fixedScreen",
				name: "fixedScreen",
				meta: {
					title: "定屏监控",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/disciplineBusiness/fixedScreen/index.vue"),
			},
			{
				path: "patrol",
				name: "patrol",
				meta: {
					title: "巡控登记",
				},
				redirect: "/patrol/patrolControl",
				component: () => import("@/view/disciplineBusiness/patrol/index.vue"),
				children: [
					{
						path: "patrolControl",
						name: "patrolControl",
						meta: {
							title: "巡控登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/patrol/patrolControl/index.vue"),
					},
					{
						path: "patrolLedger",
						name: "patrolLedger",
						meta: {
							title: "巡控登记台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/patrol/patrolLedger/index.vue"),
					}
				]
			},
			{
				path: "handover",
				name: "handover",
				meta: {
					title: "巡控交接班",
				},
				redirect: "/handover/shiftHandover",
				component: () => import("@/view/disciplineBusiness/handover/index.vue"),
				children: [
					{
						path: "shiftHandover",
						name: "shiftHandover",
						meta: {
							title: "交班台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/handover/shiftHandover/index.vue"),
					},
					{
						path: "takeOver",
						name: "takeOver",
						meta: {
							title: "接班台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/handover/takeOver/index.vue"),
					}
				]
			},
			{
				path: "/equipmentRepair",
				name: "equipmentRepair",
				meta: {
					title: "设备报修",
				},
				redirect: "/equipmentRepair/repair",
				component: () => import("@/view/disciplineBusiness/patrol/index.vue"),
				children: [
					{
						path: "repair",
						name: "repair",
						meta: {
							title: "监室设备维修管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/equipmentRepair/repair.vue"),
					},
					{
						path: "disposed",
						name: "disposed",
						meta: {
							title: "设备维修管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/equipmentRepair/disposed.vue"),
					},
				]
			},
			{
				path: "/familyContact",
				name: "familyContact",
				meta: {
					title: "家属通信",
				},
				redirect: "/familyContact/receiving",
				component: () => import("@/view/disciplineBusiness/patrol/index.vue"),
				children: [
					{
						path: "receiving",
						name: "receiving",
						meta: {
							title: "收信管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/familyContact/receiving/index.vue"),
					},
					{
						path: "send",
						name: "send",
						meta: {
							title: "寄信管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/familyContact/send/index.vue"),
					},
					{
						path: "phone",
						name: "phone",
						meta: {
							title: "亲情电话",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/familyContact/phone/index.vue"),
					}
				]
			},
			{
				path: "/libraryBorrowing",
				name: "libraryBorrowing",
				meta: {
					title: "图书借阅",
				},
				redirect: "/libraryBorrowing/borrowingManage",
				component: () => import("@/view/disciplineBusiness/patrol/index.vue"),
				children: [
					{
						path: "borrowingManage",
						name: "borrowingManage",
						meta: {
							title: "图书借阅管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/libraryBorrowing/borrowingManage/index.vue")
					},
					{
						path: "distManage",
						name: "distManage",
						meta: {
							title: "图书清单管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/libraryBorrowing/borrowList/index.vue")
					}
				]
			},
			{
				path: "/diagnosticEvaluation",
				name: "diagnosticEvaluation",
				meta: {
					title: "诊断评估",
				},
				redirect: "/diagnosticEvaluation/disciplineMedical",
				component: () => import("@/view/disciplineBusiness/diagnosticEvaluation/index.vue"),
				children: [
					{
						path: "disciplineMedical",
						name: "disciplineMedical",
						meta: {
							title: "诊断评估",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/diagnosticEvaluation/disciplineMedical.vue"),
					},
					// {
					// 	path: "disciplineMedicalLeader",
					// 	name: "disciplineMedicalLeader",
					// 	meta: {
					// 	title: "诊断评估",
					// 	menu: true,
					// 	bread: true
					// 	},
					// 	component: () => import("@/view/disciplineBusiness/diagnosticEvaluation/disciplineMedicalLeader.vue"),
					// },
					{
						path: "monthlyAssessments",
						name: "monthlyAssessments",
						meta: {
							title: "人员表现月度考核",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/diagnosticEvaluation/monthlyAssessments.vue"),
					},
					{
						path: "assessmentdossier",
						name: "assessmentdossier",
						meta: {
							title: "诊断评估档案",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/diagnosticEvaluation/assessmentdossier.vue"),
					}
				]
			},
			{
				path: "/educationalRehabilitation",
				name: "educationalRehabilitation",
				meta: {
					title: "教育康复",
				},
				redirect: "/educationalRehabilitation/courseTypeManagement",
				component: () => import("@/view/disciplineBusiness/educationalRehabilitation/index.vue"),
				children: [
					{
						path: "courseTypeManagement",
						name: "courseTypeManagement",
						meta: {
							title: "课程类型管理",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/educationalRehabilitation/courseTypeManagement.vue"),
					},
					{
						path: "educationalRehabilitationSchedule",
						name: "educationalRehabilitationSchedule",
						meta: {
							title: "教育康复课表",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/educationalRehabilitation/educationalRehabilitationSchedule.vue"),
					},
					{
						path: "educationalRehabilitationActivities",
						name: "educationalRehabilitationActivities",
						meta: {
							title: "教育康复活动",
							menu: true,
							bread: true
						},
						component: () => import("@/view/disciplineBusiness/educationalRehabilitation/educationalRehabilitationActivities.vue"),
					},


				]
			},
			{

				path: "substanceAbuse",
				name: "substanceAbuse",
				meta: {
					title: "药物滥用",
				},
				component: () => import("@/view/disciplineBusiness/substanceAbuse/index.vue"),

			},
			{
				path: "violationAppeals",
				name: "violationAppeals",
				meta: {
					title: "违规申诉",
				},
				component: () => import("@/view/disciplineBusiness/violationAppeals/index.vue"),
			},
			{
				path: "religiousWorship",
				name: "religiousWorship",
				meta: {
					title: "宗教活动",
				},
				component: () => import("@/view/disciplineBusiness/religiousWorship/index.vue"),
			},
			{
				path: "fjcy",
				name: "fjcy",
				meta: {
					title: "分级处遇",
				},
				redirect: "/fjcy",
				component: () => import("@/view/disciplineBusiness/fjcy/index.vue"),
				children: [
					{
						path: "home",
						name: "home",
						meta: {
							title: "风险研判首页",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/fjcy/home/<USER>")
					},
					{
						path: "modelManage",
						name: "modelManage",
						meta: {
							title: "积分模型设置",
							menu: false,
							bread: false,
						},
						component: () => import("@/view/disciplineBusiness/fjcy/modelManage/index.vue")
					},
					{
						path: "levelManage",
						name: "levelManage",
						meta: {
							title: "等级设置",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/fjcy/levelManage/index.vue")
					},
					{
						path: "ledger",
						name: "ledger",
						meta: {
							title: "积分台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/disciplineBusiness/fjcy/ledger/index.vue"),
					},
				],
			},
			{
				path: "cocasePersonnel",
				name: "cocasePersonnel",
				meta: {
					title: "同案人员管理",
				},
				component: () => import("@/view/disciplineBusiness/cocasePersonnel/index.vue"),
			},
			{
				path: "rightsGuarantee",
				name: "rightsGuarantee",
				meta: {
					title: "权益保障",
				},
				component: () => import("@/view/disciplineBusiness/rightsGuarantee/index.vue"),

			},
			{
				path: "civilizedCell",
				name: "civilizedCell",
				meta: {
					title: "文明监室",
				},
				component: () => import("@/view/disciplineBusiness/civilization/civilizedCell.vue"),
			},
			{
				path: "civilizedIndividuals",
				name: "civilizedIndividuals",
				meta: {
					title: "文明个人",
				},
				component: () => import("@/view/disciplineBusiness/civilization/civilizedIndividuals.vue"),
			},
			{
				path: "groupinout",
				name: "groupinout",
				meta: {
					title: "集体出入",
				},
				component: () => import("@/view/disciplineBusiness/groupinout/index.vue"),
			},
			{
				path: "educationRead",
				name: "educationRead",
				meta: {
					title: "教育读本管理",
				},
				component: () => import("@/view/disciplineBusiness/educationRead/index.vue"),
			},
			{
				path: "tzjb",
				name: "tzjb",
				meta: {
					title: "通知交办",
				},
				component: () => import("@/view/disciplineBusiness/tzjb/index.vue"),
			},
			{
				path: "jgzsk",
				name: "jgzsk",
				meta: {
					title: "监管知识库",
				},
				component: () => import("@/view/disciplineBusiness/jgzsk/index.vue"),
			},
		],
	}
];
