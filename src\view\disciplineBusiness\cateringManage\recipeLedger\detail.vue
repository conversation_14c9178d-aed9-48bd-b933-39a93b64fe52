<template>
    <div class="recipe-ledger-detail">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-content">
                <Spin size="large" />
                <div class="loading-text">正在加载菜谱详情...</div>
            </div>
        </div>

        <!-- 页面内容 -->
        <div v-else>
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="header-title">
                    <div class="title-left">
                        <Icon type="md-restaurant" size="24" color="#2b5fda" />
                        <span>菜谱台账详情</span>
                    </div>
                    <div class="title-right">
                        <div class="header-info">
                            <Icon type="md-calendar" color="#19be6b" />
                            <span>{{ query.cookTime }}</span>
                        </div>
                        <Button
                            type="primary"
                            size="small"
                            :loading="exportLoading"
                            @click="handleExportImage"
                            class="export-btn">
                            <Icon type="md-download" />
                            导出
                        </Button>
                    </div>
                </div>
            </div>

            <div class="main-content">
            <!-- 基本信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <Icon type="md-information-circle" size="20" color="#2b5fda" />
                    <span>基本信息</span>
                </div>
                <div class="card-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">
                                <Icon type="md-time" />
                                <span>安排时间</span>
                            </div>
                            <div class="info-value">{{ query.addTime || '-' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <Icon type="md-person" />
                                <span>安排人</span>
                            </div>
                            <div class="info-value">{{ query.addUserName || '-' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <Icon type="md-calendar" />
                                <span>菜谱时间</span>
                            </div>
                            <div class="info-value">{{ query.cookTime || '-' }}</div>
                        </div>
                        <div class="info-item full-width">
                            <div class="info-label">
                                <Icon type="md-copy" />
                                <span>复用模板</span>
                            </div>
                            <div class="info-value">{{ query.copyTemplateName || '-' }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 菜谱信息卡片 -->
            <div class="recipe-card">
                <div class="card-header">
                    <Icon type="md-nutrition" size="20" color="#2b5fda" />
                    <span>菜谱安排</span>
                    <div class="recipe-stats">
                        <div class="stat-item">
                            <span class="stat-label">餐次</span>
                            <span class="stat-value">{{ cookMeals.length }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">菜品</span>
                            <span class="stat-value">{{ getTotalDishCount() }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-content">
                    <div class="recipe-table-wrapper">
                        <div class="recipe-table-container">
                            <table class="recipe-table">
                                <thead>
                                    <tr class="table-header">
                                        <th class="meal-header">
                                            <div class="header-content">
                                                <Icon type="md-time" color="#2b5fda" />
                                                <span>餐次</span>
                                            </div>
                                        </th>
                                        <th v-for="(item, index) in weekList" :key="index" class="day-header">
                                            <div class="day-content">
                                                <div class="day-name">{{ item.name }}</div>
                                                <div class="day-date">{{ formatDate(index) }}</div>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(menuitem, menuIndex) in cookMeals" :key="menuIndex" class="meal-row">
                                        <td class="meal-cell">
                                            <div class="meal-info">
                                                <div class="meal-icon">
                                                    <Icon :type="getMealIcon(menuitem.name)" color="#2b5fda" />
                                                </div>
                                                <div class="meal-name">{{ menuitem.name }}</div>
                                            </div>
                                        </td>
                                        <td v-for="(item, index) in cookSubSNew.filter(item => item.mealName === menuitem.name)[0] && cookSubSNew.filter(item => item.mealName === menuitem.name)[0].cookWeekSubs"
                                            :key="index"
                                            class="dish-cell">
                                            <div class="dishes-container">
                                                <div class="dish-item" v-for="(info, idx) in item.cooks" :key="idx">
                                                    <div class="dish-category-indicator" :style="getDishCategoryStyle(info.cateId)"></div>
                                                    <div class="dish-content">
                                                        <div class="dish-name" :title="info.name">{{ info.name }}</div>
                                                        <div class="dish-category-name" :style="getCategoryTagStyle(info.cateId)">{{ getCategoryName(info.cateId) }}</div>
                                                    </div>
                                                </div>
                                                <div class="empty-placeholder" v-if="!item.cooks || item.cooks.length === 0">
                                                    <Icon type="md-restaurant" color="#c5c8ce" />
                                                    <span>暂无安排</span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="bsp-base-fotter" v-if="!autoExport">
                <Button @click="onback">关闭</Button>
            </div>
        </div>
    </div>
</template>

<script>
import lineInfo from '../component/lineInfo.vue'
import { mapActions } from 'vuex'
import dayjs from 'dayjs'
import draggable from 'vuedraggable'
import exportMixin from '@/mixins/exportMixin'
export default {
    name: "recipeLedgerDetail",
    mixins: [exportMixin],
    props:{
      cookId:{
        default: '',
        type: String
      },
      autoExport: {
        default: false,
        type: Boolean
      }
    },
    data() {
        return {
            loading: true, // 添加加载状态
            exportExecuted: false, // 防止重复导出
            weekList: [],
            cookMeals: [
                {
                    name: "早餐",
                    weekId: "1,2,3,4,5,6,7",
                    sortNo: 1
                },
                {
                    name: "午餐",
                    weekId: "1,2,3,4,5,6,7",
                    sortNo: 2
                },
                {
                    name: "晚餐",
                    weekId: "1,2,3,4,5,6,7",
                    sortNo: 3
                }
            ],
            cookSubSNew: [],
            query: {
              addTime: '',
              addUserName: '',
              cookTime: '',
              copyTemplateName: ''
            },
            // 分类数据和颜色管理
            caterClassifyList: [],
            categoryColorMap: {},
            colorPool: [
                '#3097ff', '#ed8433', '#33bb83', '#8e6bee', '#f56c6c',
                '#409eff', '#67c23a', '#e6a23c', '#f78989', '#9c88ff',
                '#36cfc9', '#ff9c6e', '#73d13d', '#ff85c0', '#597ef7'
            ]
        }
    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),

        // 获取周数据
        async handleGetZdCPZS() {
            try {
                const res = await this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_CPZS.js" })
                let week = eval('(' + res + ')')
                this.weekList = week()
            } catch (error) {
                console.error('获取周数据失败:', error)
            }
        },

        // 获取分类列表
        async handleGetClassifyList() {
            try {
                let params = {
                    cateName: "",
                    orgCode: "",
                }
                const res = await this.authPostRequest({
                    url: this.$path.catering_list,
                    params
                })
                if (res.success) {
                    this.caterClassifyList = res.data || []
                    this.initializeCategoryColors()
                }
            } catch (error) {
                console.error('获取分类列表失败:', error)
            }
        },

        // 初始化分类颜色
        initializeCategoryColors() {
            const savedColors = localStorage.getItem('categoryColorMap')
            if (savedColors) {
                try {
                    this.categoryColorMap = JSON.parse(savedColors)
                } catch (e) {
                    console.warn('加载颜色映射失败:', e)
                }
            }

            if (this.caterClassifyList && this.caterClassifyList.length > 0) {
                this.caterClassifyList.forEach((item, index) => {
                    if (!this.categoryColorMap[item.id]) {
                        const colorIndex = index % this.colorPool.length
                        this.$set(this.categoryColorMap, item.id, this.colorPool[colorIndex])
                    }
                })
                this.saveCategoryColors()
            }
        },

        // 保存颜色映射
        saveCategoryColors() {
            try {
                localStorage.setItem('categoryColorMap', JSON.stringify(this.categoryColorMap))
            } catch (e) {
                console.warn('保存颜色映射失败:', e)
            }
        },

        // 获取分类颜色 - 优先使用数据库中的颜色
        getCategoryColor(cateId) {
            // 首先查找分类数据中是否有颜色信息
            const category = this.caterClassifyList.find(item => item.id === cateId)
            if (category && category.cateColor) {
                return category.cateColor
            }

            // 如果本地缓存中有颜色，使用缓存的颜色
            if (this.categoryColorMap[cateId]) {
                return this.categoryColorMap[cateId]
            }

            // 默认颜色
            return '#3097ff'
        },

        // 获取菜品分类样式
        getDishCategoryStyle(cateId) {
            const color = this.getCategoryColor(cateId)
            return {
                backgroundColor: color,
                boxShadow: `inset 0 0 0 1px ${color}40`
            }
        },

        // 获取分类标签样式
        getCategoryTagStyle(cateId) {
            const color = this.getCategoryColor(cateId)
            return {
                background: `linear-gradient(135deg, ${color}20, ${color}10)`,
                border: `1px solid ${color}40`,
                color: color
            }
        },

        // 获取分类名称
        getCategoryName(cateId) {
            const category = this.caterClassifyList.find(item => item.id === cateId)
            return category ? category.cateName : '未知分类'
        },

        // 获取餐次图标
        getMealIcon(mealName) {
            const iconMap = {
                '早餐': 'md-sunny',
                '午餐': 'md-partly-sunny',
                '晚餐': 'md-moon'
            }
            return iconMap[mealName] || 'md-restaurant'
        },

        // 格式化日期
        formatDate(dayIndex) {
            if (!this.query.cookTime) return ''
            const startDate = this.query.cookTime.split(' -- ')[0]
            if (!startDate) return ''

            const date = dayjs(startDate).add(dayIndex, 'day')
            return date.format('MM-DD')
        },

        // 获取菜品总数
        getTotalDishCount() {
            let total = 0
            this.cookSubSNew.forEach(meal => {
                if (meal.cookWeekSubs) {
                    meal.cookWeekSubs.forEach(day => {
                        if (day.cooks) {
                            total += day.cooks.length
                        }
                    })
                }
            })
            return total
        },

        // 菜谱模板详情
        async handleGetRecipeTemDetail(id) {
            try {
                const res = await this.authGetRequest({ url: this.$path.catering_recipe_tem_get, params: { id: id } })
                if (res.success) {
                    this.cookMeals = res.data.cookMeals
                    this.query.addTime = res.data.addTime
                    this.query.addUserName = res.data.addUserName
                    this.query.cookTime = dayjs(res.data.startTime).format('YYYY-MM-DD') + ' -- ' +  dayjs(res.data.endTime).format('YYYY-MM-DD')
                    this.query.copyTemplateName = res.data.copyTemplateName
                    this.cookSubSNew = JSON.parse(JSON.stringify(res.data.cookSubs))
                }
            } catch (error) {
                console.error('获取菜谱详情失败:', error)
            }
        },

        // 导出图片功能
        async handleExportImage() {
            try {
                const fileName = this.generateExportFileName(
                    '菜谱台账详情', // 可选的前缀名称
                    this.query.cookTime,
                    this.query.copyTemplateName
                )
                await this.exportElementToImage('.main-content', fileName)

                // 如果是自动导出，导出完成后自动关闭页面
                if (this.autoExport) {
                    setTimeout(() => {
                        this.$emit('close', true) // 传递true表示导出成功
                    }, 1000) // 延迟1秒让用户看到成功提示
                }
            } catch (error) {
                // 如果是自动导出且失败，也要关闭页面
                if (this.autoExport) {
                    setTimeout(() => {
                        this.$emit('close', false) // 传递false表示导出失败
                    }, 1000)
                }
                throw error
            }
        },

        // 检查是否需要自动导出
        async checkAutoExport() {
            if (this.autoExport && !this.loading && !this.exportExecuted) {
                this.exportExecuted = true // 标记已执行，防止重复

                // 等待页面完全渲染
                await this.$nextTick()
                await new Promise(resolve => setTimeout(resolve, 500))

                try {
                    // 执行自动导出
                    await this.handleExportImage()
                } catch (error) {
                    this.exportExecuted = false // 如果失败，重置状态允许重试
                    throw error
                }
            }
        },

        // 返回
        onback(){
          this.$emit('close', false)
        }
    },

    components: {
        lineInfo,
        draggable
    },

    async created() {
        try {
            console.log('开始加载菜谱台账详情页面数据...')
            const startTime = Date.now()

            // 并行加载所有数据，提高页面加载性能
            const promises = [
                this.handleGetZdCPZS(),
                this.handleGetClassifyList(),
                this.handleGetRecipeTemDetail(this.cookId)
            ]

            // 等待所有数据加载完成
            await Promise.all(promises)

            const endTime = Date.now()
            console.log(`菜谱台账详情页面数据加载完成，耗时: ${endTime - startTime}ms`)
        } catch (error) {
            console.error('菜谱台账详情页面初始化失败:', error)
        } finally {
            // 无论成功还是失败，都要关闭加载状态
            this.loading = false
        }
    },

    watch: {
        // 监听loading状态变化，当数据加载完成后检查自动导出
        loading(newVal, oldVal) {
            if (oldVal === true && newVal === false) {
                this.$nextTick(() => {
                    this.checkAutoExport()
                })
            }
        }
    },

    computed: {},

}

</script>

<style scoped lang="less">
.recipe-ledger-detail {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 加载状态样式
    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        background: #f8f9fa;

        .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;

            .loading-text {
                font-size: 16px;
                color: #6b7280;
                font-weight: 500;
            }
        }
    }

    // 页面头部
    .page-header {
        background: #ffffff;
        border: 1px solid #cee0f0;
        border-bottom: none;
        padding: 20px;
        flex-shrink: 0;

        .header-title {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title-left {
                display: flex;
                align-items: center;

                > span {
                    font-size: 20px;
                    font-weight: 600;
                    color: #2b3346;
                    margin-left: 8px;
                }
            }

            .title-right {
                display: flex;
                align-items: center;
                gap: 16px;

                .header-info {
                    display: flex;
                    align-items: center;
                    padding: 8px 16px;
                    border-radius: 20px;
                    background: linear-gradient(135deg, #f0f9ff, #e6f3ff);
                    color: #19be6b;
                    font-size: 14px;
                    font-weight: 500;

                    span {
                        margin-left: 6px;
                    }
                }

                .export-btn {
                    background: linear-gradient(135deg, #2b5fda, #1e4ba8);
                    border: none;
                    border-radius: 20px;
                    font-size: 14px;
                    font-weight: 500;
                    box-shadow: 0 2px 8px rgba(43, 95, 218, 0.3);
                    transition: all 0.3s ease;

                    &:hover {
                        background: linear-gradient(135deg, #1e4ba8, #2b5fda);
                        box-shadow: 0 4px 12px rgba(43, 95, 218, 0.4);
                        transform: translateY(-1px);
                    }

                    &:active {
                        transform: translateY(0);
                    }

                    .ivu-icon {
                        margin-right: 4px;
                    }
                }
            }
        }
    }

    // 主要内容区域
    .main-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    // 信息卡片通用样式
    .info-card, .recipe-card {
        background: #ffffff;
        border: 1px solid #e9edf5;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 20px 24px;
            background: linear-gradient(135deg, #f8faff, #f0f6ff);
            border-bottom: 1px solid #e9edf5;

            > span {
                font-size: 18px;
                font-weight: 600;
                color: #2b3346;
                margin-left: 8px;
            }

            .recipe-stats {
                margin-left: auto;
            }
        }

        .card-content {
            padding: 24px;
        }
    }

    // 基本信息样式
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;

            &.full-width {
                grid-column: 1 / -1;
            }

            .info-label {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 14px;
                font-weight: 500;
                color: #6b7280;

                .ivu-icon {
                    font-size: 16px;
                    color: #9ca3af;
                }
            }

            .info-value {
                font-size: 16px;
                font-weight: 500;
                color: #2b3346;
                padding: 12px 16px;
                background: #f8faff;
                border: 1px solid #e9edf5;
                border-radius: 8px;
                min-height: 20px;
                display: flex;
                align-items: center;
            }
        }
    }

    // 菜谱统计信息
    .recipe-stats {
        display: flex;
        gap: 20px;

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .stat-label {
                font-size: 12px;
                color: #6b7280;
                font-weight: 500;
            }

            .stat-value {
                font-size: 18px;
                font-weight: 600;
                color: #2b5fda;
            }
        }
    }

    // 菜谱表格样式
    .recipe-table-wrapper {
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        background: #ffffff;
    }

    .recipe-table-container {
        overflow-x: auto;
        /*max-height: 600px;*/
        overflow-y: auto;
    }

    .recipe-table {
        width: 100%;
        min-width: 1200px;
        border-collapse: collapse;
        background: #ffffff;

        .table-header {
            background: linear-gradient(135deg, #f8faff, #f0f6ff);
            border-bottom: 2px solid #e2e8f0;

            th {
                padding: 18px 12px;
                border: 1px solid #e2e8f0;
                font-weight: 700;
                color: #2d3748;
                text-align: center;
                position: sticky;
                top: 0;
                z-index: 10;
                background: inherit;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                &.meal-header {
                    width: 120px;
                    min-width: 120px;
                }

                &.day-header {
                    width: 180px;
                    min-width: 180px;
                }

                .header-content {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 6px;
                    font-size: 14px;
                }

                .day-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 4px;

                    .day-name {
                        font-size: 14px;
                        font-weight: 600;
                        color: #2b3346;
                    }

                    .day-date {
                        font-size: 12px;
                        color: #6b7280;
                        font-weight: 400;
                    }
                }
            }
        }

        .meal-row {
            &:nth-child(even) {
                .dish-cell {
                    background: #fafbfc;
                }
            }

            &:hover {
                .dish-cell {
                    background: #f0f9ff;
                }
            }

            .meal-cell {
                padding: 20px 16px;
                border: 1px solid #e9edf5;
                background: #ffffff;
                position: sticky;
                left: 0;
                z-index: 5;

                .meal-info {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;

                    .meal-icon {
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        background: linear-gradient(135deg, #f0f6ff, #e6f3ff);
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .ivu-icon {
                            font-size: 16px;
                        }
                    }

                    .meal-name {
                        font-size: 14px;
                        font-weight: 600;
                        color: #2b3346;
                    }
                }
            }

            .dish-cell {
                padding: 18px 14px;
                border: 1px solid #e9edf5;
                vertical-align: top;
                min-height: 120px;
                background: #ffffff;

                .dishes-container {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    min-height: 80px;
                }

                .dish-item {
                    position: relative;
                    background: linear-gradient(135deg, #ffffff, #fafbfc);
                    border: 1px solid #e1e8f0;
                    border-radius: 10px;
                    padding: 14px 14px 14px 22px;
                    transition: all 0.3s ease;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

                    &:hover {
                        border-color: #2b5fda;
                        box-shadow: 0 4px 12px rgba(43, 95, 218, 0.15);
                        transform: translateY(-2px);
                        background: linear-gradient(135deg, #ffffff, #f8faff);
                    }

                    .dish-category-indicator {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 5px;
                        height: 100%;
                        border-radius: 0 6px 6px 0;
                        box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
                    }

                    .dish-content {
                        display: flex;
                        flex-direction: column;
                        gap: 6px;

                        .dish-name {
                            font-size: 15px;
                            font-weight: 600;
                            color: #1a202c;
                            line-height: 1.4;
                            letter-spacing: 0.2px;
                        }

                        .dish-category-name {
                            font-size: 11px;
                            font-weight: 600;
                            padding: 3px 10px;
                            border-radius: 14px;
                            display: inline-block;
                            width: fit-content;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                            transition: all 0.2s ease;
                        }
                    }
                }

                .empty-placeholder {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                    height: 80px;
                    color: #9ca3af;
                    font-size: 12px;
                    background: #fafbfc;
                    border: 2px dashed #e2e8f0;
                    border-radius: 8px;
                    font-weight: 500;

                    .ivu-icon {
                        font-size: 28px;
                        color: #d1d5db;
                    }
                }
            }
        }
    }

    // 底部按钮区域
    .bsp-base-fotter {
        padding: 16px 20px;
        background: #ffffff;
        border-top: 1px solid #e9edf5;
        display: flex;
        justify-content: center;
        gap: 12px;
        flex-shrink: 0; // 防止被压缩
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    }
}
</style>
