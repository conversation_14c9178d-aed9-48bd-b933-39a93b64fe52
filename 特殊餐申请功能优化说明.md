# 特殊餐申请功能优化说明

## 优化概述

本次优化主要针对特殊餐申请功能，实现了批量申请特殊餐的功能，提升了用户体验和操作效率。

## 问题修复

### 已修复的问题
1. **样式问题**：完善了伤情登记样式的参考，使用了正确的表单样式和布局
2. **指定日期问题**：修复了指定日期的实现，保持与原特殊餐申请的一致性，使用RadioGroup和CheckboxGroup组合

## 主要改进

### 1. 新增批量申请功能
- 在特殊餐列表页面新增"批量申请特殊餐"按钮
- 支持同时为多个被监管人员申请特殊餐
- 使用统一的表单配置，提高操作效率

### 2. 布局优化
- 采用左右分栏布局，参考伤情登记页面的设计风格
- 左侧：人员选择区域，支持多选被监管人员
- 右侧：表单填写区域，按一行一列的方式排列

### 3. 人员选择组件
- 复用风险评估页面的人员选择组件
- 支持多选功能，可以同时选择多个被监管人员
- 提供直观的人员列表展示和删除功能

### 4. 表单优化
- 表单字段按以下顺序一行一列排列：
  - 配餐类型
  - 配餐时间
  - 用餐时段
  - 指定日期（使用RadioGroup选择类型，CheckboxGroup选择具体星期）
  - 申请原因
- 支持日期范围选择和多选功能
- 增强了表单验证和用户体验
- 保持与原特殊餐申请的指定日期逻辑一致

### 5. 接口升级
- 使用新的批量保存接口：`/pam/cook/specialApply/createBatchSpecialApply`
- 支持一次性为多个人员创建特殊餐申请
- 优化了数据传输格式和处理逻辑

## 技术实现

### 文件结构
```
src/view/disciplineBusiness/cateringManage/specialMealRequest/
├── index.vue                    # 列表页面（已修改）
├── create.vue                   # 新增批量申请页面
└── components/
    ├── ChoosePeople.vue         # 人员选择组件
    └── index.js                 # 组件导出文件
```

### 路由配置
- 新增路由：`specialMealRequestCreate`
- 路径：`/disciplineBusiness/cateringManage/specialMealRequestCreate`
- 标题：批量申请特殊餐

### 接口配置
- 在 `src/path/catering.js` 中新增批量保存接口配置
- 接口地址：`specialApply_createBatch`

## 使用说明

### 操作流程
1. 进入特殊餐管理页面
2. 点击"批量申请特殊餐"按钮
3. 在左侧选择需要申请特殊餐的被监管人员
4. 在右侧填写特殊餐申请信息：
   - 选择配餐类型
   - 设置配餐时间范围
   - 选择用餐时段（可多选）
   - 设置指定日期类型和具体日期
   - 填写申请原因
5. 点击"确定"提交申请

### 数据格式
批量申请接口的数据格式：
```javascript
{
  "jgrybInfo": [
    {
      "jgrybm": "人员编码",
      "roomId": "监室ID"
    }
  ],
  "mealType": "配餐类型",
  "mealStartTime": "配餐开始时间",
  "mealEndTime": "配餐结束时间",
  "mealPeriod": "用餐时段（逗号分隔）",
  "specifiedDateType": "指定日期类型（1=全周，2=工作日）",
  "specifiedDate": "指定星期（逗号分隔，如：1,2,3,4,5）",
  "reason": "申请原因"
}
```

### 指定日期说明
- 指定日期类型：1=全周（周一到周日），2=工作日（周一到周五）
- 指定星期：1=周一，2=周二，3=周三，4=周四，5=周五，6=周六，7=周日
- 当选择指定日期类型时，会自动设置对应的星期选择

## 注意事项

1. 确保选择了至少一个被监管人员才能提交申请
2. 所有必填字段都需要填写完整
3. 日期选择支持范围选择和多选功能
4. 用餐时段可以多选（早餐、午餐、晚餐）
5. 提交成功后会自动返回列表页面

## 兼容性

- 保持原有单人申请功能不变
- 新增的批量申请功能作为补充
- 所有现有功能和接口保持兼容

## 测试建议

1. 测试人员选择功能是否正常
2. 测试表单验证是否有效
3. 测试批量提交是否成功
4. 测试页面跳转是否正确
5. 测试与现有功能的兼容性
