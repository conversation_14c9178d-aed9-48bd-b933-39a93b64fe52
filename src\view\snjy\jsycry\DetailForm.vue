<template>
  <div class="psychiatric-management-detail">
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :responsive="true"
      @card-action="handleCardAction"
      @bottom-action="handleBottomAction"
      @collapse-change="handleCollapseChange"
    >
      <!-- 左侧人员信息面板 -->
      <template #left>
        <personnel-selector
          v-model="personInfo.jgrybm"
          title="人员信息"
          :enableScan=false
          :personnelType=ryzt
          :show-case-info="false"
          :disabled="true"
          mode="detail"
          placeholder="人员信息"
        />
      </template>

      <!-- 业务详情表单 -->
      <template #business-detail>
        <DynamicForm
          ref="businessForm"
          v-model="businessFormData"
          :config="businessFormConfig"
          :mode="formMode"
          :loading="loading"
        />
      </template>
    </DetailCardLayout>
  </div>
</template>

<script>
import {FIELD_TYPES, FORM_MODES} from '@/components/dynamic-form/types'
import {mapActions} from 'vuex'

export default {
  name: 'PsychiatricManagementDetail',
  props: {
    pk: String,
    mode: {
      type: String,
      default: 'view'
    }
  },
  data() {
    return {
      ryzt: 'ZS',
      loading: false,
      formMode: FORM_MODES.VIEW,

      // 人员信息
      personInfo: {
        jgrybm: ''
      },

      // 左侧面板配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true,
        data: {}
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'business-detail',
          title: '重点病人管理',
          icon: 'ios-medical',
          iconColor: '#5b8ff9',
          slot: 'business-detail',
          showHeader: false
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        }
      ],

      // 业务详情表单数据
      businessFormData: {
        // 基本信息
        jgrybm: '', // 被监管人员编码
        jgryxm: '', // 监管人员姓名

        // 精神病相关信息
        cnsltPreDiag: '', // 会诊初步诊断 字典ZD_JSB_HZCBZD
        controlReason: '', // 列控原因
        controlTime: '', // 列控时间
        managementLevel: '', // 列管等级 字典：ZD_JSB_LGDJ
        treatmentOutcome: '', // 治疗后是否好转 1是0否

        // 经办信息
        operatePolice: '', // 登记民警
        operatePoliceSfzh: '', // 登记民警身份证号
        operateTime: '', // 登记时间
        status: '' // 状态
      }
    }
  },

  computed: {
    // 业务详情表单配置 - 参考新增表单配置，但设置为只读
    businessFormConfig() {
      return [
        {
          title: '精神病异常管理详情',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showTitle: true,
          fields: [
            {
              key: 'cnsltPreDiag',
              label: '会诊初步诊断',
              type: FIELD_TYPES.DICTIONARY,
              required: false, // 保持与新增表单一致的必填状态
              span: 12,
              dicName: 'ZD_JSB_HZCBZD', // 字典：会诊初步诊断
              disabled: true,
              props: {
                placeholder: '会诊初步诊断',
                multiple: false,
                disabled: true
              }
            },
            {
              key: 'controlTime',
              label: '列控时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: false, // 保持与新增表单一致的必填状态
              disabled: true,
              span: 12,
              props: {
                placeholder: '列控时间',
                format: 'yyyy-MM-dd',
                disabled: true
              }
            },
            {
              key: 'controlReason',
              label: '列控原因',
              type: FIELD_TYPES.TEXTAREA,
              required: false, // 保持与新增表单一致的必填状态
              span: 24,
              disabled: true,
              props: {
                placeholder: '列控原因',
                rows: 3,
                disabled: true
              }
            },
            {
              key: 'managementLevel',
              label: '列管等级',
              type: FIELD_TYPES.DICTIONARY,
              required: false, // 保持与新增表单一致的必填状态
              span: 12,
              dicName: 'ZD_JSB_LGDJ', // 字典：列管等级
              disabled: true,
              props: {
                placeholder: '列管等级',
                multiple: false,
                disabled: true
              }
            },
            {
              key: 'treatmentOutcome',
              label: '治疗后是否好转',
              type: FIELD_TYPES.DICTIONARY,
              required: false, // 保持与新增表单一致的必填状态
              span: 12,
              dicName: 'ZD_TYSFDM', // 字典：是否
              disabled: true,
              props: {
                placeholder: '治疗后是否好转',
                multiple: false,
                disabled: true
              }
            },
            {
              key: 'operatePolice',
              label: '登记民警',
              type: FIELD_TYPES.INPUT,
              span: 12,
              disabled: true,
              props: {
                placeholder: '登记民警',
                disabled: true
              }
            },
            {
              key: 'operateTime',
              label: '登记时间',
              type: FIELD_TYPES.INPUT,
              span: 12,
              disabled: true,
              props: {
                placeholder: '登记时间',
                disabled: true
              }
            }
          ]
        }
      ]
    }
  },

  mounted() {
    this.initializeData()
  },

  methods: {
    ...mapActions(['authGetRequest']),

    // 初始化数据
    initializeData() {
      if (this.pk) {
        this.loadRecordData(this.pk)
      }
    },

    // 加载记录数据
    loadRecordData(recordId) {
      this.loading = true
      this.authGetRequest({
        url: this.$path.psychiatric_get,
        params: {id: recordId}
      }).then(res => {
        this.loading = false
        if (res.success && res.data) {
          // 合并数据，保留现有字段
          this.businessFormData = {...this.businessFormData, ...res.data}
          this.personInfo.jgrybm = res.data.jgrybm || ''

          // 如果有人员姓名，也更新到人员信息中
          if (res.data.jgryxm) {
            this.businessFormData.jgryxm = res.data.jgryxm
          }
        } else {
          this.$Message.error(res.msg || '加载数据失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('加载数据失败：', error)
      })
    },

    // 处理收缩变化
    handleCollapseChange(event) {
      console.log('面板收缩状态变化:', event)
    },

    // 处理卡片操作
    handleCardAction(event) {
      console.log('卡片操作:', event)
    },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleBack()
          break
      }
    },

    // 返回
    handleBack() {
      this.$emit('toback')
    }
  }
}
</script>

<style lang="less" scoped>
.psychiatric-management-detail {
  height: 100%;

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }

  // 禁用状态下的样式优化
  /deep/ .ivu-input[disabled],
  /deep/ .ivu-select[disabled],
  /deep/ .ivu-date-picker[disabled] {
    background-color: #f7f7f7;
    border-color: #dcdee2;
    color: #515a6e;
  }

  // 文件上传组件在详情页的样式
  /deep/ .upload-box {
    .file-item {
      background: #f7f7f7;
      border: 1px solid #dcdee2;
    }
  }
}
</style>
