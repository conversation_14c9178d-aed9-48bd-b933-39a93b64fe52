/**
 * 导出功能混入
 * 提供通用的图片导出功能，支持单个和批量导出
 */
import html2canvas from 'html2canvas'
import dayjs from 'dayjs'

export default {
    data() {
        return {
            exportLoading: false, // 导出加载状态
            batchExportLoading: false, // 批量导出加载状态
            exportProgress: {
                current: 0,
                total: 0,
                visible: false
            }
        }
    },

    methods: {
        /**
         * 导出指定DOM元素为图片
         * @param {string} selector - DOM选择器
         * @param {string} fileName - 文件名
         * @param {Object} options - html2canvas配置选项
         */
        async exportElementToImage(selector, fileName, options = {}) {
            try {
                this.exportLoading = true
                this.$Message.loading({
                    content: '正在生成图片...',
                    duration: 0
                })

                // 获取要导出的DOM元素
                const element = document.querySelector(selector)
                if (!element) {
                    throw new Error(`未找到选择器 "${selector}" 对应的元素`)
                }

                // 默认配置
                const defaultOptions = {
                    backgroundColor: '#f8f9fa',
                    scale: 2,
                    useCORS: true,
                    allowTaint: false,
                    width: element.scrollWidth,
                    height: element.scrollHeight,
                    scrollX: 0,
                    scrollY: 0
                }

                // 合并配置
                const finalOptions = { ...defaultOptions, ...options }

                // 使用html2canvas生成图片
                const canvas = await html2canvas(element, finalOptions)

                // 下载图片
                this.downloadImageFromCanvas(canvas, fileName)
                
                this.$Message.destroy()
                this.$Message.success('导出成功！')
                
                return canvas
            } catch (error) {
                console.error('导出图片失败:', error)
                this.$Message.destroy()
                this.$Message.error('导出失败，请重试')
                throw error
            } finally {
                this.exportLoading = false
            }
        },

        /**
         * 从canvas下载图片
         * @param {HTMLCanvasElement} canvas - canvas元素
         * @param {string} fileName - 文件名
         */
        downloadImageFromCanvas(canvas, fileName) {
            try {
                canvas.toBlob((blob) => {
                    if (!blob) {
                        throw new Error('图片生成失败')
                    }

                    // 创建下载链接
                    const url = URL.createObjectURL(blob)
                    const link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.download = fileName
                    
                    // 触发下载
                    document.body.appendChild(link)
                    link.click()
                    
                    // 清理资源
                    document.body.removeChild(link)
                    URL.revokeObjectURL(url)
                }, 'image/png', 1.0)
            } catch (error) {
                console.error('下载图片失败:', error)
                throw error
            }
        },

        /**
         * 生成标准的文件名
         * @param {string} prefix - 文件名前缀，如果不传则使用默认值
         * @param {string} cookTime - 菜谱时间
         * @param {string} templateName - 模板名称，优先使用此名称
         */
        generateExportFileName(prefix = '', cookTime = '', templateName = '') {
            const timestamp = dayjs().format('YYYYMMDD_HHmmss')
            const cleanCookTime = cookTime ? cookTime.replace(/\s*--\s*/g, '至').replace(/[\/\\:*?"<>|]/g, '') : ''
            const cleanTemplateName = templateName ? templateName.replace(/[\/\\:*?"<>|]/g, '') : ''

            // 优先使用模板名称
            if (cleanTemplateName) {
                return cleanCookTime ?
                    `${cleanTemplateName}-${cleanCookTime}-${timestamp}.png` :
                    `${cleanTemplateName}-${timestamp}.png`
            }

            // 其次使用传入的前缀
            if (prefix) {
                return cleanCookTime ?
                    `${prefix}-${cleanCookTime}-${timestamp}.png` :
                    `${prefix}-${timestamp}.png`
            }

            // 最后使用通用默认名称
            const defaultName = '导出文件'
            return cleanCookTime ?
                `${defaultName}-${cleanCookTime}-${timestamp}.png` :
                `${defaultName}-${timestamp}.png`
        },

        /**
         * 批量导出功能（预留接口）
         * @param {Array} items - 要导出的数据项数组
         * @param {Function} renderCallback - 渲染回调函数
         * @param {Function} exportCallback - 导出回调函数
         */
        async batchExportImages(items, renderCallback, exportCallback) {
            try {
                this.batchExportLoading = true
                this.exportProgress = {
                    current: 0,
                    total: items.length,
                    visible: true
                }

                this.$Message.loading({
                    content: `正在批量导出 (0/${items.length})...`,
                    duration: 0
                })

                for (let i = 0; i < items.length; i++) {
                    const item = items[i]
                    this.exportProgress.current = i + 1
                    
                    this.$Message.destroy()
                    this.$Message.loading({
                        content: `正在批量导出 (${i + 1}/${items.length})...`,
                        duration: 0
                    })

                    // 渲染数据
                    if (renderCallback) {
                        await renderCallback(item, i)
                    }

                    // 等待渲染完成
                    await this.$nextTick()
                    await new Promise(resolve => setTimeout(resolve, 500))

                    // 执行导出
                    if (exportCallback) {
                        await exportCallback(item, i)
                    }
                }

                this.$Message.destroy()
                this.$Message.success(`批量导出完成！共导出 ${items.length} 个文件`)
            } catch (error) {
                console.error('批量导出失败:', error)
                this.$Message.destroy()
                this.$Message.error('批量导出失败，请重试')
                throw error
            } finally {
                this.batchExportLoading = false
                this.exportProgress.visible = false
            }
        },

        /**
         * 等待元素渲染完成
         * @param {string} selector - 选择器
         * @param {number} timeout - 超时时间(ms)
         */
        async waitForElementRender(selector, timeout = 5000) {
            return new Promise((resolve, reject) => {
                const startTime = Date.now()
                
                const checkElement = () => {
                    const element = document.querySelector(selector)
                    if (element && element.offsetHeight > 0) {
                        resolve(element)
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error(`等待元素渲染超时: ${selector}`))
                    } else {
                        setTimeout(checkElement, 100)
                    }
                }
                
                checkElement()
            })
        }
    }
}
