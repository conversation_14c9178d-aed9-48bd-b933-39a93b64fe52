<template>
  <div>
    <div class="">
      <div class="bsp-base-content" v-if="!showData">
        <s-DataGrid ref="grid" funcMark="jgzsk:list" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':jgzsk:list:add')" @click.native="addEvent('add')">新增</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }" class="btnList">
              <Button type="primary" v-if="func.includes(appCode + ':jgzsk:list:read')"  @click.native="editEvent(index,row,'read')" >阅读</Button>&nbsp;&nbsp;
              <Button type="primary" v-if="func.includes(appCode + ':jgzsk:list:edit')"   @click.native="editEvent(index,row,'edit')" >编辑</Button>&nbsp;&nbsp;
              <Button type="error" v-if="func.includes(appCode + ':jgzsk:list:del')" @click.native="delEvent(row)" >删除</Button>
          </template>
        </s-DataGrid>
      </div>
      <!-- 提讯登记 -->
      <div v-if='showData' class='InquiryTitle'>{{modalTitle}}</div>
      <addForm v-if='(saveType=="add" || saveType == "edit") && showData' :saveType="saveType" @toback='toback' :curId='curData.id' />
      <detail v-if='saveType=="info" && showData' @toback='toback' :curId='curData.id' />
      <info v-if='saveType=="read" && showData' @toback='toback' :curId='curData.id' />
	  </div>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import addForm from './addForm.vue'
import info from './info.vue'
export default {
    components: {
	  sDataGrid,
      addForm,
      info
	},
    data() {
        return {
            appName: serverConfig.APP_NAME,
            appCode: serverConfig.APP_MARK,
            appId: serverConfig.APP_ID,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            params:{},
            showData: false,
            modalTitle: '',
            saveType: '',
            loadingsignIn: false,
            fileList: [],
            formData: {
              userList: [],
              publishType: '01',
              isFullPolice: '0',
              userId:"",
              userIdName:"",
              orgCode:""
            },
            curData: {},
            ruleValidate: {},
        }
    },
    methods: {
        addEvent(type) {
          this.saveType = type
          this.modalTitle = '知识库新增'
          this.showData = true
        },
        editEvent(index,row,type){
          this.saveType = type
          this.curData = row
          if(type == 'read') {
            this.modalTitle = '监管线上知识库'
          } else if(type == 'edit') {
            this.modalTitle = '知识库编辑'
          }
          this.showData = true
        },
        delEvent(row) {
            this.$Modal.confirm({
                title: '温馨提示',
                content: '请确认是否删除?',
                onOk: () => {
                    this.$store.dispatch('authGetRequest',{
                        url: this.$path.app_knowledgeBase_delete,
                        params:{
                            ids: row.id
                        }
                    }).then(res => {
                        if(res.success) {
                            this.$nextTick(() => {
                            this.on_refresh_table()
                            })
                        } else {
                            this.$Message.error('接口操作失败!')
                        }
                    })
                }
            })
        },
        toback() {
          this.showData = false
        },
        on_refresh_table () {
          this.$refs.grid.query_grid_data(1)
        }
    },
    created() {
      console.log(this.$route.query);
      if(this.$route.query && this.$route.query.type) {
        if(!this.$route.query.bussinId) {
          this.$Modal.error({
            title: '温馨提示',
            content: '业务数据不能为空!'
          })
          return;
        }
        
        this.saveType = 'handle'
        this.curData = {
          id: this.$route.query.bussinId,
          publish_type: this.$route.query.type
        }
        this.showData = true
      }
    }
} 
</script>

<style lang="less" scoped>
.com-sub-title{
  border-left: 4px solid #3491fa;
  padding-left: 8px;
  font-size: 16px;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  position: relative;
  margin-bottom: 16px;
}
/deep/.ivu-modal-body {
  height: 500px !important;
  overflow: auto;
}
.ivu-form-item{
    margin-bottom: 10px !important;
  }
 .InquiryTitle{
    border-bottom:1px solid #dcdee2;
    padding:16px;
 }
 .ivu-table-cell-slot button{
  margin:5px 0;
 }
</style>